{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/common/util.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EACL,MAAM,EACN,IAAI,EACJ,SAAS,EACT,GAAG,EACH,QAAQ,EACR,KAAK,EACL,UAAU,EACV,KAAK,GACN,MAAM,gCAAgC,CAAC;AAExC,OAAO,EAAC,WAAW,EAAC,MAAM,mBAAmB,CAAC;AAC9C,OAAO,EAAC,cAAc,EAAC,MAAM,yBAAyB,CAAC;AACvD,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,gBAAgB,EAAE,kBAAkB,EAAC,MAAM,qBAAqB,CAAC;AAEzE,OAAO,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AACjC,OAAO,EAAC,YAAY,EAAC,MAAM,aAAa,CAAC;AAOzC,OAAO,EAAC,YAAY,EAAC,MAAM,iBAAiB,CAAC;AAE7C;;GAEG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAEnD;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAC,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAC,CAAC,CAAC;AAEzE;;GAEG;AACH,MAAM,UAAU,GAAG,MAAM,CAAC,6CAA6C,CAAC,CAAC;AAEzE;;GAEG;AACH,MAAM,OAAO,YAAY;IACvB,MAAM,CAAC,YAAY,GAAG,eAAe,CAAC;IAEtC,MAAM,CAAC,YAAY,CACjB,YAAoB,EACpB,IAAqB;QAErB,MAAM,GAAG,GAAG,IAAI,YAAY,EAAE,CAAC;QAC/B,GAAG,CAAC,aAAa,GAAG,YAAY,CAAC;QACjC,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClC,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAC,KAAK,GAAG,CAAC,GAAW,EAAgB,EAAE;QAC3C,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAChC,MAAM,CAAC,YAAY,GAAG,EAAE,EAAE,UAAU,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5D,MAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;QACxC,YAAY,CAAC,aAAa,GAAG,YAAY,CAAC;QAC1C,YAAY,CAAC,WAAW,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;QAC1D,OAAO,YAAY,CAAC;IACtB,CAAC,CAAC;IAEF,MAAM,CAAC,cAAc,GAAG,CAAC,GAAW,EAAW,EAAE;QAC/C,OAAO,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC,CAAC;IAEF,aAAa,CAAU;IACvB,WAAW,CAAU;IAErB,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,QAAQ;QACN,OAAO,QAAQ;YACb,IAAI,CAAC,aAAa;YAClB,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC;SACrC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;IAChB,CAAC;;AAGH;;GAEG;AACH,MAAM,CAAC,MAAM,4BAA4B,GAAG,CAC1C,YAAoB,EACpB,MAAS,EACN,EAAE;IACL,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC;QAC7D,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,MAAM,QAAQ,GAAG,KAAK,CAAC,iBAAiB,CAAC;IACzC,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;QACrC,iCAAiC;QACjC,iDAAiD;QACjD,6DAA6D;QAC7D,mCAAmC;QACnC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;IACF,MAAM,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC,KAAmC,CAAC;IAC7D,KAAK,CAAC,iBAAiB,GAAG,QAAQ,CAAC;IACnC,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE;QAC3B,CAAC,UAAU,CAAC,EAAE,YAAY,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC;KAC5D,CAAC,CAAC;AACL,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,gCAAgC,GAAG,CAG9C,MAAS,EACiB,EAAE;IAC5B,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,CAAC;QAC7D,OAAO,MAAM,CAAC,UAAqB,CAAiB,CAAC;IACvD,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,GAAY,EAAiB,EAAE;IACtD,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,YAAY,MAAM,CAAC;AAC1D,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,GAAY,EAAiB,EAAE;IACtD,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,YAAY,MAAM,CAAC;AAC1D,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,CAAC,GAAY,EAA+B,EAAE;IACzE,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,EAAE,WAAW,KAAK,MAAM,CAAC;AAChE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,GAAY,EAAiB,EAAE;IACtD,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,EAAE,WAAW,KAAK,MAAM,CAAC;AAChE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,MAAM,GAAG,CAAC,GAAY,EAAe,EAAE;IAClD,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,EAAE,WAAW,KAAK,IAAI,CAAC;AAC9D,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,gBAAgB;AAC9B,sEAAsE;AACtE,GAAsB,EACtB,GAAG,IAAe;IAElB,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAClB,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,yCAAyC,CAAC,CAAC;QACrE,OAAO,GAAG,CAAC;IACb,CAAC;IAED,SAAS,iBAAiB,CAAC,GAAY;QACrC,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,CAAC;YAC9B,OAAO,WAAW,CAAC;QACrB,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED,OAAO,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AAC9D,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAC3C,QAAoC,EACpC,IAAa;IAEb,MAAM,OAAO,GAAiB,EAAE,CAAC;IACjC,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAC;IACpC,IAAI,IAAI,EAAE,CAAC;QACT,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACxE,IAAI,CAAC;YACH,OAAO,IAAI,EAAE,CAAC;gBACZ,MAAM,EAAC,IAAI,EAAE,KAAK,EAAC,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBAC1C,IAAI,IAAI,EAAE,CAAC;oBACT,MAAM;gBACR,CAAC;gBACD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACpB,MAAM,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,MAAM,UAAU,CAAC,KAAK,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,EAAE,CAAC;YACZ,MAAM,EAAC,IAAI,EAAE,KAAK,EAAC,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;YAC1C,IAAI,IAAI,EAAE,CAAC;gBACT,MAAM;YACR,CAAC;YACD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IACD,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACzC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,UAAU,CAAC,KAAK,CAAC,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AAEH;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,6BAA6B,CACjD,MAAkB,EAClB,MAAc;IAEd,OAAO,IAAI,cAAc,CAAC;QACxB,KAAK,CAAC,IAAI,CAAC,UAAU;YACnB,MAAM,EAAC,IAAI,EAAE,aAAa,EAAE,GAAG,EAAC,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE;gBAC9D,MAAM;aACP,CAAC,CAAC;YAEH,UAAU,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,EAAE,aAAa,IAAI,KAAK,CAAC,CAAC,CAAC;YACrE,IAAI,GAAG,EAAE,CAAC;gBACR,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,EAAC,MAAM,EAAC,CAAC,CAAC;gBACxC,UAAU,CAAC,KAAK,EAAE,CAAC;YACrB,CAAC;QACH,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAChC,IAAY;IAEZ,IAAI,UAAU,GAAG,IAAI,CAAC;IACtB,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC;QAC/B,OAAO;QACP,SAAS;QACT,QAAQ;QACR,cAAc;KACf,CAAC,CAAC;IAEH,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/B,UAAU,GAAG,IAAI,CAAC;IACpB,CAAC;IACD,MAAM,CAAC,UAAU,EAAE,mCAAmC,IAAI,EAAE,CAAC,CAAC;IAC9D,OAAO,UAA6D,CAAC;AACvE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,OAAO,CAAC,EAAU,EAAE,KAAa;IAC/C,OAAO,EAAE,KAAK,CAAC;QACb,CAAC,CAAC,KAAK;QACP,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CACZ,GAAG,CAAC,GAAG,EAAE;YACP,MAAM,IAAI,YAAY,CAAC,2BAA2B,EAAE,IAAI,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC;QACrE,CAAC,CAAC,CACH,CAAC;AACR,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAC7B,6BAA6B,GAAG,cAAc,CAAC;AAEjD;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAC3B,uDAAuD,CAAC;AAC1D;;GAEG;AACH,MAAM,UAAU,mBAAmB,CAAC,GAAW;IAC7C,OAAO,iBAAiB,GAAG,EAAE,CAAC;AAChC,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,GAAG,CAAC;AAErC;;GAEG;AACH,MAAM,UAAU,eAAe,CAC7B,UAAsB,EAAE,EACxB,aAA0B,IAAI;IAE9B,MAAM,QAAQ,GAA0D;QACtE,KAAK,EAAE,CAAC;QACR,mBAAmB,EAAE,KAAK;QAC1B,cAAc,EAAE,EAAE;QAClB,cAAc,EAAE,EAAE;QAClB,eAAe,EAAE,KAAK;QACtB,SAAS,EAAE,KAAK;QAChB,UAAU,EAAE,EAAE;QACd,iBAAiB,EAAE,KAAK;QACxB,cAAc,EAAE,KAAK;QACrB,OAAO,EAAE,KAAK;QACd,MAAM,EAAE,IAAI;QACZ,YAAY,EAAE,IAAI;KACnB,CAAC;IAEF,IAAI,KAAK,GAAG,GAAG,CAAC;IAChB,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,MAAM,MAAM,GACV,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAA0B,CAAC,CAChE,UAAU,CACX,CAAC;QACJ,MAAM,CAAC,MAAM,EAAE,wBAAwB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAC1D,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QACrB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IACzB,CAAC;SAAM,CAAC;QACN,KAAK,GAAG,6BAA6B,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,KAAK,CAAC;QAC1E,MAAM;YACJ,6BAA6B,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,IAAI,MAAM,CAAC;IACxE,CAAC;IAED,MAAM,MAAM,GAAG;QACb,GAAG,EAAE,6BAA6B,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC;QACxE,IAAI,EAAE,6BAA6B,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC;QAC1E,MAAM,EACJ,6BAA6B,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC;QACxE,KAAK,EACH,6BAA6B,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC;KACxE,CAAC;IAEF,yEAAyE;IACzE,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;IACxB,CAAC;IAED,OAAO;QACL,GAAG,QAAQ;QACX,GAAG,OAAO;QACV,KAAK;QACL,MAAM;QACN,MAAM;KACP,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG;IAC1B,EAAE,EAAE,CAAC;IACL,EAAE,EAAE,EAAE;IACN,EAAE,EAAE,IAAI;IACR,EAAE,EAAE,IAAI;CACT,CAAC;AAEF,SAAS,6BAA6B,CACpC,SAA2B,EAC3B,aAA0B,IAAI;IAE9B,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE,CAAC;QACrC,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,IAAI,MAAM,CAAC;IACX,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QACxB,wEAAwE;QACxE,MAAM,GAAG,SAAS,CAAC;IACrB,CAAC;SAAM,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QAC/B,MAAM,IAAI,GAAG,SAAS,CAAC;QACvB,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QACzD,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,IAAI,IAAI,YAAY,EAAE,CAAC;YACzB,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,gFAAgF;YAChF,wDAAwD;YACxD,IAAI,GAAG,IAAI,CAAC;YACZ,SAAS,GAAG,IAAI,CAAC;QACnB,CAAC;QACD,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;QAChC,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,mCAAmC,GAAG,IAAI,CAAC,CAAC;QAClE,MAAM,GAAG,KAAK,GAAG,YAAY,CAAC,IAAiC,CAAC,CAAC;IACnE,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CACb,2CAA2C,GAAG,OAAO,SAAS,CAC/D,CAAC;IACJ,CAAC;IACD,OAAO,MAAM,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;AAC3C,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAG9B,OAA6B,EAAE,SAAgB;IAC/C,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC,EAAE;QACjC,MAAM,QAAQ,GAAG,CAAC,KAAoB,EAAE,EAAE;YACxC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC,CAAC;QACF,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAChC,OAAO,GAAG,EAAE;YACV,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QACnC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe,CAC7B,MAAoB,EACpB,KAAa;IAEb,OAAO,MAAM;QACX,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,IAAI,CAC7B,GAAG,CAAC,GAAG,EAAE;YACP,IAAI,MAAM,CAAC,MAAM,YAAY,KAAK,EAAE,CAAC;gBACnC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;gBAC5B,MAAM,MAAM,CAAC,MAAM,CAAC;YACtB,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,EAAC,KAAK,EAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CACH;QACH,CAAC,CAAC,KAAK,CAAC;AACZ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW,CACzB,SAAuD;IAEvD,OAAO,QAAQ,CAAmB,CAAC,KAAK,EAAiB,EAAE;QACzD,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CACjD,MAAM,CAAC,OAAO,CAAC,EAAE;YACf,OAAO,OAAO,CAAC;QACjB,CAAC,CAAC,EACF,GAAG,CAAC,GAAG,EAAE;YACP,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC"}