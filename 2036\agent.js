

const { program } = require('commander');
const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { exec, spawn } = require('child_process');
const readline = require('readline');
const os = require('os');
const { promisify } = require('util');
const execAsync = promisify(exec);
const chalk = require('chalk');
const ora = require('ora');
const { v4: uuidv4 } = require('uuid');
const cheerio = require('cheerio');
const { createServer } = require('http-server');
const { JSDOM } = require('jsdom');
const glob = require('glob');
const crypto = require('crypto');

// Environment detection and configuration
const DEBUG = process.env.DEBUG === 'true';
const IS_WINDOWS = process.platform === 'win32';
const IS_MAC = process.platform === 'darwin';
const IS_LINUX = process.platform === 'linux';

// ═══════════════════════════════════════════════════════════════════════════════
// 🔧 CONFIGURATION & INITIALIZATION
// ═══════════════════════════════════════════════════════════════════════════════

const CONFIG_DIR = path.join(os.homedir(), '.ai-agent');
const DEFAULT_CONFIG = {
  apiKey: process.env.GEMINI_API_KEY || 'AIzaSyDOE7pTDMrVPGmNlkuCpgFav-85hjsrTtw',
  model: process.env.GEMINI_MODEL || 'gemini-2.0-flash-exp',
  temperature: 0.7,
  maxOutputTokens: 8192,
  topK: 40,
  topP: 0.95,
  historyPath: path.join(CONFIG_DIR, 'history.json'),
  configPath: path.join(CONFIG_DIR, 'config.json'),
  memoriesPath: path.join(CONFIG_DIR, 'memories'),
  contextPath: path.join(CONFIG_DIR, 'context'),
  tasksPath: path.join(CONFIG_DIR, 'tasks'),
  logsPath: path.join(CONFIG_DIR, 'logs'),
  maxHistorySize: 100,
  maxContextSize: 50,
  debug: DEBUG,
  autoSaveHistory: true,
  autoSaveContext: true,
  streamResponses: true,
  defaultTimeout: 60000,
  userPrompt: chalk.cyan('┌─ ') + chalk.bold.white('You') + chalk.cyan(' ─> '),
  agentPrompt: chalk.green('└─ ') + chalk.bold.green('Agent') + chalk.green(' ─> '),
  thinkingPrompt: chalk.yellow('🧠 ') + chalk.italic.yellow('Thinking...'),
  showTimestamps: true,
  showProgress: true
};

// Initialize configuration
let config = { ...DEFAULT_CONFIG };

// ═══════════════════════════════════════════════════════════════════════════════
// 🧠 ENHANCED MEMORY & CONTEXT SYSTEM
// ═══════════════════════════════════════════════════════════════════════════════

class AdvancedMemorySystem {
  constructor() {
    this.conversationMemory = [];
    this.taskMemory = new Map();
    this.contextMemory = new Map();
    this.longTermMemory = new Map();
    this.workingMemory = new Map();
    this.intentHistory = [];
    this.toolUsageStats = new Map();
    this.lastContext = null;
  }

  // Store conversation context with smart compression
  addConversationMemory(role, content, metadata = {}) {
    const memory = {
      id: uuidv4(),
      role,
      content,
      timestamp: Date.now(),
      metadata,
      importance: this.calculateImportance(content),
      embedding: this.generateSimpleEmbedding(content)
    };

    this.conversationMemory.push(memory);

    // Auto-compress if memory gets too large
    if (this.conversationMemory.length > config.maxHistorySize) {
      this.compressConversationMemory();
    }
  }

  // Calculate importance score for memory prioritization
  calculateImportance(content) {
    let score = 1;

    // Higher importance for code, errors, and commands
    if (content.includes('```') || content.includes('error') || content.includes('failed')) score += 2;
    if (content.includes('function') || content.includes('class') || content.includes('import')) score += 1;
    if (content.length > 500) score += 1;

    return Math.min(score, 5);
  }

  // Simple embedding generation for semantic search
  generateSimpleEmbedding(text) {
    const words = text.toLowerCase().split(/\s+/);
    const embedding = new Array(100).fill(0);

    words.forEach((word, index) => {
      const hash = this.simpleHash(word);
      embedding[hash % 100] += 1;
    });

    return embedding;
  }

  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  // Compress conversation memory by removing less important entries
  compressConversationMemory() {
    this.conversationMemory.sort((a, b) => b.importance - a.importance);
    this.conversationMemory = this.conversationMemory.slice(0, Math.floor(config.maxHistorySize * 0.8));
  }

  // Store task-specific memory
  addTaskMemory(taskId, data) {
    this.taskMemory.set(taskId, {
      ...data,
      timestamp: Date.now(),
      id: taskId
    });
  }

  // Store context for smart retrieval
  addContext(key, value, category = 'general') {
    this.contextMemory.set(key, {
      value,
      category,
      timestamp: Date.now(),
      accessCount: 0
    });
  }

  // Retrieve relevant context based on current situation
  getRelevantContext(query, limit = 5) {
    const queryEmbedding = this.generateSimpleEmbedding(query);
    const contexts = Array.from(this.contextMemory.entries());

    const scored = contexts.map(([key, context]) => {
      const similarity = this.cosineSimilarity(queryEmbedding, this.generateSimpleEmbedding(context.value));
      return { key, context, similarity };
    });

    return scored
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit)
      .map(item => ({ key: item.key, ...item.context }));
  }

  cosineSimilarity(a, b) {
    const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0);
    const magnitudeA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0));
    const magnitudeB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0));
    return dotProduct / (magnitudeA * magnitudeB) || 0;
  }

  // Save memory to disk
  async saveMemory() {
    try {
      const memoryData = {
        conversation: this.conversationMemory,
        tasks: Array.from(this.taskMemory.entries()),
        context: Array.from(this.contextMemory.entries()),
        longTerm: Array.from(this.longTermMemory.entries()),
        intentHistory: this.intentHistory,
        toolStats: Array.from(this.toolUsageStats.entries())
      };

      await fs.promises.writeFile(
        path.join(config.memoriesPath, 'advanced_memory.json'),
        JSON.stringify(memoryData, null, 2)
      );
    } catch (error) {
      logger.error('Failed to save memory:', error.message);
    }
  }

  // Load memory from disk
  async loadMemory() {
    try {
      const memoryPath = path.join(config.memoriesPath, 'advanced_memory.json');
      if (fs.existsSync(memoryPath)) {
        const memoryData = JSON.parse(await fs.promises.readFile(memoryPath, 'utf8'));

        this.conversationMemory = memoryData.conversation || [];
        this.taskMemory = new Map(memoryData.tasks || []);
        this.contextMemory = new Map(memoryData.context || []);
        this.longTermMemory = new Map(memoryData.longTerm || []);
        this.intentHistory = memoryData.intentHistory || [];
        this.toolUsageStats = new Map(memoryData.toolStats || []);
      }
    } catch (error) {
      logger.error('Failed to load memory:', error.message);
    }
  }
}

// Global memory system instance
const memorySystem = new AdvancedMemorySystem();

/**
 * Ensures all required directories exist
 */
function ensureDirectories() {
  const directories = [
    CONFIG_DIR,
    path.dirname(config.configPath),
    config.memoriesPath,
    config.contextPath,
    config.tasksPath,
    config.logsPath
  ];

  for (const dir of directories) {
    if (!fs.existsSync(dir)) {
      try {
        fs.mkdirSync(dir, { recursive: true });
        if (config.debug) {
          console.log(chalk.gray(`📁 Created directory: ${dir}`));
        }
      } catch (error) {
        console.error(chalk.red(`❌ Error creating directory ${dir}:`), error.message);
      }
    }
  }
}

/**
 * Loads configuration from file
 */
function loadConfig() {
  if (fs.existsSync(config.configPath)) {
    try {
      const savedConfig = JSON.parse(fs.readFileSync(config.configPath, 'utf8'));
      config = { ...DEFAULT_CONFIG, ...savedConfig };
      if (config.debug) {
        console.log(chalk.gray('Configuration loaded successfully'));
      }
    } catch (error) {
      console.error(chalk.red('Error loading config:'), error.message);
      console.log(chalk.yellow('Using default configuration'));
    }
  } else {
    saveConfig(); // Save default config if none exists
  }
}

/**
 * Saves current configuration to file
 */
function saveConfig() {
  try {
    fs.writeFileSync(config.configPath, JSON.stringify(config, null, 2), 'utf8');
    if (config.debug) {
      console.log(chalk.gray('Configuration saved successfully'));
    }
    return true;
  } catch (error) {
    console.error(chalk.red('Error saving config:'), error.message);
    return false;
  }
}

// Initialize directories and load configuration
ensureDirectories();
loadConfig();

/**
 * Dynamically imports the 'open' package for browser opening
 * This is done dynamically to avoid requiring it when not needed
 * @returns {Promise<Function>} The open function
 */
async function importOpen() {
  try {
    // Dynamic import for the 'open' package
    const openModule = await import('open');
    return openModule.default;
  } catch (error) {
    logger.error(`Failed to import 'open' package: ${error.message}`);
    logger.warn('You may need to install it with: npm install open');

    // Fallback to using the exec function
    return async (url) => {
      try {
        if (IS_WINDOWS) {
          await execAsync(`start ${url}`);
        } else if (IS_MAC) {
          await execAsync(`open ${url}`);
        } else if (IS_LINUX) {
          await execAsync(`xdg-open ${url}`);
        } else {
          throw new Error('Unsupported platform for opening URLs');
        }
      } catch (execError) {
        throw new Error(`Failed to open URL: ${execError.message}`);
      }
    };
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🌐 GLOBAL STATE MANAGEMENT
// ═══════════════════════════════════════════════════════════════════════════════

const runningCommands = new Map();
const commandHistory = new Map();
const deployments = new Map();
const activeProcesses = new Map();
const projectContext = new Map();

// AI client instances
let genAI = null;
let model = null;
let streamingModel = null;

// Enhanced UI components
const spinner = ora({
  color: 'cyan',
  spinner: 'dots12',
  hideCursor: true
});

// ═══════════════════════════════════════════════════════════════════════════════
// 🛠️ COMPREHENSIVE TOOLS SYSTEM
// ═══════════════════════════════════════════════════════════════════════════════

const tools = [
  // 🗂️ FILE SYSTEM TOOLS
  {
    name: "create_file",
    category: "File System",
    description: "Creates a new file with specified content at the given path",
    usage: `create_file("path/to/file.js", "console.log('Hello World');")`,
    parameters: {
      filePath: { type: "string", required: true, description: "Path where the file should be created" },
      content: { type: "string", required: false, default: "", description: "Initial content for the file" },
      overwrite: { type: "boolean", required: false, default: false, description: "Whether to overwrite if file exists" }
    },
    fn: async ({ filePath, content = "", overwrite = false }) => {
      try {
        if (fs.existsSync(filePath) && !overwrite) {
          return { success: false, error: `File already exists: ${filePath}. Use overwrite=true to replace.` };
        }

        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }

        await fs.promises.writeFile(filePath, content, 'utf8');
        memorySystem.addContext(`file_created_${filePath}`, { path: filePath, content: content.substring(0, 200) }, 'file_operations');

        return {
          success: true,
          message: `✅ File created successfully: ${filePath}`,
          filePath,
          size: content.length
        };
      } catch (error) {
        return { success: false, error: `Failed to create file: ${error.message}` };
      }
    }
  },

  {
    name: "edit_file",
    category: "File System",
    description: "Edits an existing file using various edit operations (replace, insert, append)",
    usage: `edit_file("file.js", { operation: "replace", search: "old code", replacement: "new code" })`,
    parameters: {
      filePath: { type: "string", required: true, description: "Path to the file to edit" },
      operation: { type: "string", required: true, enum: ["replace", "insert", "append", "prepend"], description: "Type of edit operation" },
      search: { type: "string", required: false, description: "Text to search for (for replace operation)" },
      replacement: { type: "string", required: false, description: "Replacement text" },
      content: { type: "string", required: false, description: "Content to insert/append/prepend" },
      lineNumber: { type: "number", required: false, description: "Line number for insert operation" }
    },
    fn: async ({ filePath, operation, search, replacement, content, lineNumber }) => {
      try {
        if (!fs.existsSync(filePath)) {
          return { success: false, error: `File does not exist: ${filePath}` };
        }

        let fileContent = await fs.promises.readFile(filePath, 'utf8');
        const originalLength = fileContent.length;

        switch (operation) {
          case "replace":
            if (!search || replacement === undefined) {
              return { success: false, error: "Replace operation requires 'search' and 'replacement' parameters" };
            }
            fileContent = fileContent.replace(new RegExp(search, 'g'), replacement);
            break;

          case "insert":
            if (!content || !lineNumber) {
              return { success: false, error: "Insert operation requires 'content' and 'lineNumber' parameters" };
            }
            const lines = fileContent.split('\n');
            lines.splice(lineNumber - 1, 0, content);
            fileContent = lines.join('\n');
            break;

          case "append":
            if (!content) {
              return { success: false, error: "Append operation requires 'content' parameter" };
            }
            fileContent += '\n' + content;
            break;

          case "prepend":
            if (!content) {
              return { success: false, error: "Prepend operation requires 'content' parameter" };
            }
            fileContent = content + '\n' + fileContent;
            break;

          default:
            return { success: false, error: `Unknown operation: ${operation}` };
        }

        await fs.promises.writeFile(filePath, fileContent, 'utf8');
        memorySystem.addContext(`file_edited_${filePath}`, {
          path: filePath,
          operation,
          sizeDiff: fileContent.length - originalLength
        }, 'file_operations');

        return {
          success: true,
          message: `✅ File edited successfully: ${filePath}`,
          operation,
          sizeDiff: fileContent.length - originalLength
        };
      } catch (error) {
        return { success: false, error: `Failed to edit file: ${error.message}` };
      }
    }
  },

  {
    name: "read_file",
    category: "File System",
    description: "Reads content from a file with optional line range specification",
    usage: `read_file("package.json", { startLine: 1, endLine: 10 })`,
    parameters: {
      filePath: { type: "string", required: true, description: "Path to the file to read" },
      startLine: { type: "number", required: false, description: "Starting line number (1-based)" },
      endLine: { type: "number", required: false, description: "Ending line number (1-based)" },
      encoding: { type: "string", required: false, default: "utf8", description: "File encoding" }
    },
    fn: async ({ filePath, startLine, endLine, encoding = "utf8" }) => {
      try {
        if (!fs.existsSync(filePath)) {
          return { success: false, error: `File does not exist: ${filePath}` };
        }

        let content = await fs.promises.readFile(filePath, encoding);

        if (startLine || endLine) {
          const lines = content.split('\n');
          const start = startLine ? startLine - 1 : 0;
          const end = endLine ? endLine : lines.length;
          content = lines.slice(start, end).join('\n');
        }

        const stats = await fs.promises.stat(filePath);
        memorySystem.addContext(`file_read_${filePath}`, {
          path: filePath,
          size: stats.size,
          lastModified: stats.mtime
        }, 'file_operations');

        return {
          success: true,
          content,
          filePath,
          size: stats.size,
          lines: content.split('\n').length,
          lastModified: stats.mtime
        };
      } catch (error) {
        return { success: false, error: `Failed to read file: ${error.message}` };
      }
    }
  },

  {
    name: "create_directory",
    category: "File System",
    description: "Creates a directory structure with optional nested folders",
    usage: `create_directory("src/components/ui", { recursive: true })`,
    parameters: {
      dirPath: { type: "string", required: true, description: "Directory path to create" },
      recursive: { type: "boolean", required: false, default: true, description: "Create parent directories if needed" }
    },
    fn: async ({ dirPath, recursive = true }) => {
      try {
        if (fs.existsSync(dirPath)) {
          return { success: false, error: `Directory already exists: ${dirPath}` };
        }

        await fs.promises.mkdir(dirPath, { recursive });
        memorySystem.addContext(`dir_created_${dirPath}`, { path: dirPath }, 'file_operations');

        return {
          success: true,
          message: `✅ Directory created: ${dirPath}`,
          path: dirPath
        };
      } catch (error) {
        return { success: false, error: `Failed to create directory: ${error.message}` };
      }
    }
  },

  {
    name: "list_dir",
    category: "File System",
    description: "Lists contents of a directory with optional filtering and detailed information",
    usage: `list_dir("./src", { recursive: true, includeHidden: false })`,
    parameters: {
      dirPath: { type: "string", required: true, description: "Directory path to list" },
      recursive: { type: "boolean", required: false, default: false, description: "List subdirectories recursively" },
      includeHidden: { type: "boolean", required: false, default: false, description: "Include hidden files/folders" },
      filter: { type: "string", required: false, description: "File extension filter (e.g., '.js')" }
    },
    fn: async ({ dirPath, recursive = false, includeHidden = false, filter }) => {
      try {
        if (!fs.existsSync(dirPath)) {
          return { success: false, error: `Directory does not exist: ${dirPath}` };
        }

        const listFiles = async (dir, depth = 0) => {
          const items = [];
          const entries = await fs.promises.readdir(dir, { withFileTypes: true });

          for (const entry of entries) {
            if (!includeHidden && entry.name.startsWith('.')) continue;
            if (filter && !entry.name.endsWith(filter)) continue;

            const fullPath = path.join(dir, entry.name);
            const stats = await fs.promises.stat(fullPath);

            const item = {
              name: entry.name,
              path: fullPath,
              type: entry.isDirectory() ? 'directory' : 'file',
              size: stats.size,
              modified: stats.mtime,
              depth
            };

            items.push(item);

            if (recursive && entry.isDirectory() && depth < 10) {
              const subItems = await listFiles(fullPath, depth + 1);
              items.push(...subItems);
            }
          }

          return items;
        };

        const items = await listFiles(dirPath);

        return {
          success: true,
          items,
          count: items.length,
          directory: dirPath
        };
      } catch (error) {
        return { success: false, error: `Failed to list directory: ${error.message}` };
      }
    }
  },

  {
    name: "file_search",
    category: "File System",
    description: "Search for files using glob patterns with advanced filtering",
    usage: `file_search("**/*.js", { exclude: ["node_modules/**"], maxResults: 50 })`,
    parameters: {
      pattern: { type: "string", required: true, description: "Glob pattern to search for" },
      baseDir: { type: "string", required: false, default: ".", description: "Base directory to search from" },
      exclude: { type: "array", required: false, default: [], description: "Patterns to exclude" },
      maxResults: { type: "number", required: false, default: 100, description: "Maximum number of results" }
    },
    fn: async ({ pattern, baseDir = ".", exclude = [], maxResults = 100 }) => {
      try {
        const options = {
          cwd: baseDir,
          ignore: exclude,
          absolute: true
        };

        const files = glob.sync(pattern, options).slice(0, maxResults);

        const results = await Promise.all(files.map(async (file) => {
          const stats = await fs.promises.stat(file);
          return {
            path: file,
            relativePath: path.relative(baseDir, file),
            size: stats.size,
            modified: stats.mtime,
            extension: path.extname(file)
          };
        }));

        return {
          success: true,
          files: results,
          count: results.length,
          pattern,
          baseDir
        };
      } catch (error) {
        return { success: false, error: `File search failed: ${error.message}` };
      }
    }
  },

  // 💻 TERMINAL & SHELL TOOLS
  {
    name: "run_in_terminal",
    category: "Terminal",
    description: "Execute shell commands with full control over execution environment",
    usage: `run_in_terminal("npm install", { cwd: "./project", timeout: 30000 })`,
    parameters: {
      command: { type: "string", required: true, description: "Command to execute" },
      cwd: { type: "string", required: false, description: "Working directory" },
      timeout: { type: "number", required: false, default: 60000, description: "Timeout in milliseconds" },
      shell: { type: "string", required: false, description: "Shell to use" },
      env: { type: "object", required: false, description: "Environment variables" },
      async: { type: "boolean", required: false, default: false, description: "Run asynchronously" }
    },
    fn: async ({ command, cwd = process.cwd(), timeout = 60000, shell, env, async: isAsync = false }) => {
      const commandId = uuidv4();

      try {
        const execOptions = {
          cwd,
          timeout,
          maxBuffer: 10 * 1024 * 1024, // 10MB
          env: { ...process.env, ...env }
        };

        if (shell) execOptions.shell = shell;

        runningCommands.set(commandId, {
          command,
          cwd,
          status: 'running',
          startTime: Date.now(),
          pid: null
        });

        if (isAsync) {
          // Run asynchronously
          const childProcess = spawn(command, [], {
            ...execOptions,
            stdio: ['ignore', 'pipe', 'pipe'],
            shell: true
          });

          runningCommands.get(commandId).pid = childProcess.pid;
          activeProcesses.set(commandId, childProcess);

          let stdout = '';
          let stderr = '';

          childProcess.stdout.on('data', (data) => {
            stdout += data.toString();
          });

          childProcess.stderr.on('data', (data) => {
            stderr += data.toString();
          });

          childProcess.on('close', (code) => {
            const result = {
              ...runningCommands.get(commandId),
              status: code === 0 ? 'completed' : 'failed',
              exitCode: code,
              stdout,
              stderr,
              endTime: Date.now()
            };

            commandHistory.set(commandId, result);
            runningCommands.delete(commandId);
            activeProcesses.delete(commandId);
          });

          return {
            success: true,
            commandId,
            message: `🚀 Command started asynchronously: ${command}`,
            async: true
          };
        } else {
          // Run synchronously
          const { stdout, stderr } = await execAsync(command, execOptions);

          const result = {
            command,
            cwd,
            status: 'completed',
            stdout,
            stderr,
            startTime: runningCommands.get(commandId).startTime,
            endTime: Date.now()
          };

          commandHistory.set(commandId, result);
          runningCommands.delete(commandId);

          memorySystem.addContext(`command_${commandId}`, {
            command,
            success: true,
            output: stdout.substring(0, 500)
          }, 'terminal_operations');

          return {
            success: true,
            commandId,
            stdout,
            stderr,
            message: `✅ Command completed: ${command}`
          };
        }
      } catch (error) {
        const result = {
          command,
          cwd,
          status: 'failed',
          error: error.message,
          startTime: runningCommands.get(commandId)?.startTime || Date.now(),
          endTime: Date.now()
        };

        commandHistory.set(commandId, result);
        runningCommands.delete(commandId);

        return {
          success: false,
          commandId,
          error: error.message,
          stderr: error.stderr || ''
        };
      }
    }
  },

  // 🌐 WEB & SEARCH TOOLS (No external APIs, using axios + cheerio + jsdom)
  {
    name: "fetch_webpage",
    category: "Web",
    description: "Fetch and parse webpage content using fast HTTP requests and DOM parsing",
    usage: `fetch_webpage("https://example.com", { parseContent: true, followRedirects: true })`,
    parameters: {
      url: { type: "string", required: true, description: "URL to fetch" },
      parseContent: { type: "boolean", required: false, default: true, description: "Parse HTML content" },
      followRedirects: { type: "boolean", required: false, default: true, description: "Follow HTTP redirects" },
      timeout: { type: "number", required: false, default: 10000, description: "Request timeout in ms" },
      headers: { type: "object", required: false, description: "Custom headers" }
    },
    fn: async ({ url, parseContent = true, followRedirects = true, timeout = 10000, headers = {} }) => {
      try {
        const defaultHeaders = {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
          ...headers
        };

        const response = await axios.get(url, {
          timeout,
          headers: defaultHeaders,
          maxRedirects: followRedirects ? 5 : 0,
          validateStatus: (status) => status < 400
        });

        let result = {
          success: true,
          url,
          status: response.status,
          headers: response.headers,
          content: response.data,
          size: response.data.length
        };

        if (parseContent && response.headers['content-type']?.includes('text/html')) {
          const $ = cheerio.load(response.data);

          result.parsed = {
            title: $('title').text().trim(),
            description: $('meta[name="description"]').attr('content') || '',
            headings: $('h1, h2, h3').map((i, el) => $(el).text().trim()).get(),
            links: $('a[href]').map((i, el) => ({
              text: $(el).text().trim(),
              href: $(el).attr('href')
            })).get().slice(0, 50),
            images: $('img[src]').map((i, el) => ({
              alt: $(el).attr('alt') || '',
              src: $(el).attr('src')
            })).get().slice(0, 20),
            text: $('body').text().replace(/\s+/g, ' ').trim().substring(0, 5000)
          };
        }

        memorySystem.addContext(`webpage_${url}`, {
          url,
          title: result.parsed?.title || 'Unknown',
          fetchTime: Date.now()
        }, 'web_operations');

        return result;
      } catch (error) {
        return {
          success: false,
          error: `Failed to fetch webpage: ${error.message}`,
          url
        };
      }
    }
  },

  {
    name: "semantic_web_search",
    category: "Web",
    description: "Perform intelligent web searches using multiple search engines without APIs",
    usage: `semantic_web_search("JavaScript async await tutorial", { maxResults: 10 })`,
    parameters: {
      query: { type: "string", required: true, description: "Search query" },
      maxResults: { type: "number", required: false, default: 10, description: "Maximum results to return" },
      searchEngine: { type: "string", required: false, default: "duckduckgo", enum: ["duckduckgo", "bing"], description: "Search engine to use" }
    },
    fn: async ({ query, maxResults = 10, searchEngine = "duckduckgo" }) => {
      try {
        let searchUrl;

        if (searchEngine === "duckduckgo") {
          searchUrl = `https://html.duckduckgo.com/html/?q=${encodeURIComponent(query)}`;
        } else if (searchEngine === "bing") {
          searchUrl = `https://www.bing.com/search?q=${encodeURIComponent(query)}`;
        }

        const response = await axios.get(searchUrl, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          },
          timeout: 15000
        });

        const $ = cheerio.load(response.data);
        const results = [];

        if (searchEngine === "duckduckgo") {
          $('.result').each((i, element) => {
            if (results.length >= maxResults) return false;

            const $element = $(element);
            const title = $element.find('.result__title a').text().trim();
            const url = $element.find('.result__title a').attr('href');
            const snippet = $element.find('.result__snippet').text().trim();

            if (title && url) {
              results.push({ title, url, snippet, source: 'DuckDuckGo' });
            }
          });
        } else if (searchEngine === "bing") {
          $('.b_algo').each((i, element) => {
            if (results.length >= maxResults) return false;

            const $element = $(element);
            const title = $element.find('h2 a').text().trim();
            const url = $element.find('h2 a').attr('href');
            const snippet = $element.find('.b_caption p').text().trim();

            if (title && url) {
              results.push({ title, url, snippet, source: 'Bing' });
            }
          });
        }

        memorySystem.addContext(`search_${query}`, {
          query,
          resultCount: results.length,
          searchTime: Date.now()
        }, 'web_operations');

        return {
          success: true,
          query,
          results,
          count: results.length,
          searchEngine
        };
      } catch (error) {
        return {
          success: false,
          error: `Web search failed: ${error.message}`,
          query
        };
      }
    }
  },

  // 🧠 AI & REASONING TOOLS
  {
    name: "natural_language_to_code",
    category: "AI & Reasoning",
    description: "Convert natural language descriptions into working code",
    usage: `natural_language_to_code("Create a function that sorts an array of objects by name", "javascript")`,
    parameters: {
      description: { type: "string", required: true, description: "Natural language description of the code" },
      language: { type: "string", required: false, default: "javascript", description: "Programming language" },
      framework: { type: "string", required: false, description: "Framework or library context" },
      style: { type: "string", required: false, default: "modern", enum: ["modern", "classic", "functional"], description: "Code style preference" }
    },
    fn: async ({ description, language = "javascript", framework, style = "modern" }) => {
      try {
        const prompt = `
Convert this natural language description into ${language} code:
"${description}"

${framework ? `Framework/Library: ${framework}` : ''}
Style: ${style}

Requirements:
- Write clean, readable, and well-commented code
- Follow ${language} best practices
- Include error handling where appropriate
- Make the code production-ready

Respond with ONLY the code, no explanations or markdown formatting.
`;

        const result = await model.generateContent(prompt);
        const code = result.response.text().trim();

        memorySystem.addContext(`code_generated_${Date.now()}`, {
          description,
          language,
          codeLength: code.length
        }, 'ai_operations');

        return {
          success: true,
          code,
          language,
          description,
          framework,
          style
        };
      } catch (error) {
        return {
          success: false,
          error: `Code generation failed: ${error.message}`
        };
      }
    }
  },

  {
    name: "intent_recognition",
    category: "AI & Reasoning",
    description: "Analyze user input to understand intent and extract key information",
    usage: `intent_recognition("I want to create a React component for a login form")`,
    parameters: {
      input: { type: "string", required: true, description: "User input to analyze" },
      context: { type: "string", required: false, description: "Additional context for better understanding" }
    },
    fn: async ({ input, context }) => {
      try {
        const prompt = `
Analyze this user input and extract the intent and key information:
"${input}"

${context ? `Context: ${context}` : ''}

Identify:
1. Primary intent (what the user wants to do)
2. Secondary intents (related actions)
3. Entities (specific things mentioned)
4. Technology stack (if mentioned)
5. Urgency level (low/medium/high)
6. Complexity level (simple/medium/complex)

Respond in JSON format:
{
  "primaryIntent": "string",
  "secondaryIntents": ["string"],
  "entities": ["string"],
  "technologies": ["string"],
  "urgency": "low|medium|high",
  "complexity": "simple|medium|complex",
  "suggestedActions": ["string"]
}
`;

        const result = await model.generateContent(prompt);
        const analysisText = result.response.text().trim();

        // Try to parse JSON response
        let analysis;
        try {
          const jsonMatch = analysisText.match(/\{[\s\S]*\}/);
          analysis = JSON.parse(jsonMatch ? jsonMatch[0] : analysisText);
        } catch (parseError) {
          // Fallback to simple analysis
          analysis = {
            primaryIntent: "general_request",
            secondaryIntents: [],
            entities: [],
            technologies: [],
            urgency: "medium",
            complexity: "medium",
            suggestedActions: ["analyze_further"]
          };
        }

        memorySystem.addContext(`intent_${Date.now()}`, {
          input,
          primaryIntent: analysis.primaryIntent,
          complexity: analysis.complexity
        }, 'ai_operations');

        return {
          success: true,
          input,
          analysis,
          confidence: 0.8 // Simple confidence score
        };
      } catch (error) {
        return {
          success: false,
          error: `Intent recognition failed: ${error.message}`
        };
      }
    }
  }
];

// ═══════════════════════════════════════════════════════════════════════════════
// 📝 ENHANCED LOGGING SYSTEM
// ═══════════════════════════════════════════════════════════════════════════════

const logger = {
  debug: (message, ...args) => {
    if (config.debug) {
      const timestamp = config.showTimestamps ? chalk.gray(`[${new Date().toISOString()}]`) : '';
      console.log(`${timestamp} ${chalk.gray('🔍 DEBUG:')} ${chalk.gray(message)}`, ...args);
    }
  },
  info: (message, ...args) => {
    const timestamp = config.showTimestamps ? chalk.gray(`[${new Date().toISOString()}]`) : '';
    console.log(`${timestamp} ${chalk.blue('ℹ️  INFO:')} ${chalk.white(message)}`, ...args);
  },
  success: (message, ...args) => {
    const timestamp = config.showTimestamps ? chalk.gray(`[${new Date().toISOString()}]`) : '';
    console.log(`${timestamp} ${chalk.green('✅ SUCCESS:')} ${chalk.green(message)}`, ...args);
  },
  warn: (message, ...args) => {
    const timestamp = config.showTimestamps ? chalk.gray(`[${new Date().toISOString()}]`) : '';
    console.log(`${timestamp} ${chalk.yellow('⚠️  WARNING:')} ${chalk.yellow(message)}`, ...args);
  },
  error: (message, ...args) => {
    const timestamp = config.showTimestamps ? chalk.gray(`[${new Date().toISOString()}]`) : '';
    console.error(`${timestamp} ${chalk.red('❌ ERROR:')} ${chalk.red(message)}`, ...args);
  },
  thinking: (message) => {
    console.log(`${chalk.yellow('🧠')} ${chalk.italic.yellow(message)}`);
  },
  tool: (toolName, status = 'running') => {
    const statusIcon = status === 'running' ? '🔄' : status === 'success' ? '✅' : '❌';
    console.log(`${statusIcon} ${chalk.cyan('TOOL:')} ${chalk.bold(toolName)} ${chalk.gray(`[${status}]`)}`);
  },
  stream: (chunk, isComplete = false) => {
    if (isComplete) {
      process.stdout.write('\n');
    } else {
      process.stdout.write(chunk);
    }
  },
  raw: (message) => {
    console.log(message);
  }
};

// ═══════════════════════════════════════════════════════════════════════════════
// 🚀 ENHANCED STREAMING SYSTEM
// ═══════════════════════════════════════════════════════════════════════════════

class StreamingResponseHandler {
  constructor() {
    this.isStreaming = false;
    this.currentResponse = '';
    this.thinkingMode = false;
  }

  async streamResponse(prompt, options = {}) {
    try {
      this.isStreaming = true;
      this.currentResponse = '';

      if (options.showThinking) {
        this.thinkingMode = true;
        logger.thinking('Processing your request...');
      }

      // Use streaming API
      const result = await model.generateContentStream(prompt);

      if (options.showThinking) {
        console.log(chalk.green('\n🤖 Agent Response:'));
        console.log(chalk.gray('─'.repeat(50)));
      }

      for await (const chunk of result.stream) {
        const chunkText = chunk.text();
        this.currentResponse += chunkText;

        if (config.streamResponses) {
          logger.stream(chunkText);
        }
      }

      if (config.streamResponses) {
        logger.stream('', true); // Complete the stream
      }

      this.isStreaming = false;
      this.thinkingMode = false;

      return {
        success: true,
        response: this.currentResponse,
        streamed: config.streamResponses
      };
    } catch (error) {
      this.isStreaming = false;
      this.thinkingMode = false;

      return {
        success: false,
        error: error.message,
        response: this.currentResponse
      };
    }
  }

  async streamWithTools(prompt, toolContext = []) {
    try {
      // Enhanced prompt with tool context
      const enhancedPrompt = `
${prompt}

Available tools: ${toolContext.map(t => t.name).join(', ')}
Context: ${JSON.stringify(memorySystem.getRelevantContext(prompt, 3))}

Please provide a thoughtful response and suggest any tools that might be helpful.
`;

      return await this.streamResponse(enhancedPrompt, { showThinking: true });
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

const streamHandler = new StreamingResponseHandler();

/**
 * Load memories from disk
 */
function loadMemories() {
  try {
    const memoryFiles = fs.readdirSync(config.memoriesPath);
    let loadedCount = 0;

    for (const file of memoryFiles) {
      if (file.endsWith('.json')) {
        try {
          const memoryId = path.basename(file, '.json');
          const memoryContent = fs.readFileSync(path.join(config.memoriesPath, file), 'utf8');
          memories[memoryId] = JSON.parse(memoryContent);
          loadedCount++;
        } catch (err) {
          logger.error(`Failed to load memory file ${file}: ${err.message}`);
        }
      }
    }

    logger.debug(`Loaded ${loadedCount} memories from ${memoryFiles.length} files`);
  } catch (error) {
    logger.error(`Error loading memories: ${error.message}`);
  }
}

/**
 * Load deployments from disk
 */
function loadDeployments() {
  if (fs.existsSync(config.deploymentConfigPath)) {
    try {
      const savedDeployments = JSON.parse(fs.readFileSync(config.deploymentConfigPath, 'utf8'));
      Object.assign(deployments, savedDeployments);
      logger.debug(`Loaded ${Object.keys(deployments).length} deployments`);
    } catch (error) {
      logger.error(`Error loading deployments: ${error.message}`);
    }
  } else {
    logger.debug('No deployments file found, starting with empty deployments');
  }
}

/**
 * Initialize AI with the configured API key and model settings
 * @param {boolean} forceReload - Whether to force reloading the model even if already initialized
 * @returns {Object} The initialized model
 */
function initializeAI(forceReload = false) {
  try {
    if (!genAI || forceReload) {
      if (!config.apiKey) {
        throw new Error('API key is not configured. Please set it in the configuration file or GEMINI_API_KEY environment variable.');
      }

      logger.debug(`Initializing AI with model: ${config.model}`);

      // Validate API key format
      if (!config.apiKey.match(/^[A-Za-z0-9_-]+$/)) {
        throw new Error('Invalid API key format. Please check your configuration.');
      }

      // Create the Generative AI client
      genAI = new GoogleGenerativeAI(config.apiKey);

      // Configure model with parameters from config
      model = genAI.getGenerativeModel({
        model: config.model,
        generationConfig: {
          temperature: config.temperature,
          maxOutputTokens: config.maxOutputTokens,
          topK: config.topK,
          topP: config.topP
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      });

      logger.debug('AI model initialized successfully');

      // Test the model with a simple prompt to ensure it's working
      testModel();
    }
    return model;
  } catch (error) {
    logger.error(`Failed to initialize AI: ${error.message}`);
    throw error;
  }
}

/**
 * Test the AI model with a simple prompt to ensure it's working
 * @returns {Promise<boolean>} Whether the test was successful
 */
async function testModel() {
  try {
    logger.debug('Testing AI model with a simple prompt...');
    const testPrompt = 'Respond with a single word: "Working"';
    const result = await model.generateContent(testPrompt);
    const response = result.response.text().trim().toLowerCase();

    if (response.includes('working')) {
      logger.debug('AI model test successful');
      return true;
    } else {
      logger.warn(`AI model test returned unexpected response: ${response}`);
      return false;
    }
  } catch (error) {
    logger.error(`AI model test failed: ${error.message}`);
    return false;
  }
}

/**
 * Manages conversation history for the AI
 */
class ConversationManager {
  constructor(maxHistorySize = config.maxHistorySize) {
    this.history = [];
    this.maxHistorySize = maxHistorySize;
    this.loadHistory();
  }

  /**
   * Add a message to the conversation history
   * @param {string} role - The role of the message sender ('user' or 'assistant')
   * @param {string} content - The message content
   */
  addMessage(role, content) {
    if (!role || !content) return;

    this.history.push({
      role,
      parts: [content],
      timestamp: new Date().toISOString()
    });

    // Trim history if it exceeds the maximum size
    if (this.history.length > this.maxHistorySize) {
      this.history = this.history.slice(this.history.length - this.maxHistorySize);
    }

    // Auto-save history if enabled
    if (config.autoSaveHistory) {
      this.saveHistory();
    }
  }

  /**
   * Get the conversation history formatted for the AI
   * @param {number} limit - Maximum number of messages to include
   * @returns {Array} Formatted conversation history
   */
  getFormattedHistory(limit = this.maxHistorySize) {
    const recentHistory = this.history.slice(-limit);

    return recentHistory.map(msg => ({
      role: msg.role,
      parts: [{ text: msg.parts[0] }]
    }));
  }

  /**
   * Clear the conversation history
   */
  clearHistory() {
    this.history = [];
    this.saveHistory();
  }

  /**
   * Save the conversation history to disk
   */
  saveHistory() {
    try {
      fs.writeFileSync(config.historyPath, JSON.stringify(this.history, null, 2), 'utf8');
      logger.debug('Conversation history saved successfully');
    } catch (error) {
      logger.error(`Failed to save conversation history: ${error.message}`);
    }
  }

  /**
   * Load the conversation history from disk
   */
  loadHistory() {
    try {
      if (fs.existsSync(config.historyPath)) {
        const savedHistory = JSON.parse(fs.readFileSync(config.historyPath, 'utf8'));
        this.history = Array.isArray(savedHistory) ? savedHistory : [];
        logger.debug(`Loaded ${this.history.length} messages from conversation history`);
      }
    } catch (error) {
      logger.error(`Failed to load conversation history: ${error.message}`);
      this.history = [];
    }
  }
}

// ═══════════════════════════════════════════════════════════════════════════════
// 🎯 SMART TOOL ORCHESTRATION ENGINE
// ═══════════════════════════════════════════════════════════════════════════════

class SmartOrchestrator {
  constructor() {
    this.executionHistory = [];
    this.currentPlan = null;
    this.contextStack = [];
  }

  // Analyze user input and create execution plan
  async createExecutionPlan(userInput) {
    try {
      // First, understand the intent
      const intentTool = tools.find(t => t.name === 'intent_recognition');
      const intentResult = await intentTool.fn({ input: userInput });

      if (!intentResult.success) {
        throw new Error('Failed to analyze intent');
      }

      const intent = intentResult.analysis;

      // Get relevant tools based on intent
      const relevantTools = this.getRelevantTools(intent);

      // Create execution plan using AI
      const planPrompt = `
User Request: "${userInput}"
Intent Analysis: ${JSON.stringify(intent)}

Available Tools:
${relevantTools.map(tool => `- ${tool.name}: ${tool.description}`).join('\n')}

Create a step-by-step execution plan. Consider:
1. What tools are needed and in what order
2. Dependencies between steps
3. Error handling and fallbacks
4. Expected outcomes

Respond with a JSON plan:
{
  "steps": [
    {
      "tool": "tool_name",
      "parameters": { /* tool parameters */ },
      "purpose": "why this step is needed",
      "dependencies": ["previous_step_ids"],
      "fallback": "alternative_approach"
    }
  ],
  "expectedOutcome": "what the user will get",
  "estimatedTime": "time estimate",
  "complexity": "low|medium|high"
}
`;

      const planResult = await streamHandler.streamResponse(planPrompt, { showThinking: true });

      if (!planResult.success) {
        throw new Error('Failed to create execution plan');
      }

      // Parse the plan
      let plan;
      try {
        const jsonMatch = planResult.response.match(/\{[\s\S]*\}/);
        plan = JSON.parse(jsonMatch ? jsonMatch[0] : planResult.response);
      } catch (parseError) {
        // Fallback to simple plan
        plan = {
          steps: [{
            tool: "natural_language_to_code",
            parameters: { description: userInput },
            purpose: "Generate response based on user input",
            dependencies: [],
            fallback: "direct_response"
          }],
          expectedOutcome: "AI-generated response",
          estimatedTime: "30 seconds",
          complexity: "medium"
        };
      }

      this.currentPlan = plan;
      return plan;
    } catch (error) {
      logger.error('Failed to create execution plan:', error.message);
      return null;
    }
  }

  // Get tools relevant to the current intent
  getRelevantTools(intent) {
    const { primaryIntent, technologies, complexity } = intent;

    let relevantTools = [];

    // File operations
    if (primaryIntent.includes('create') || primaryIntent.includes('file') || primaryIntent.includes('write')) {
      relevantTools.push(...tools.filter(t => t.category === 'File System'));
    }

    // Web operations
    if (primaryIntent.includes('fetch') || primaryIntent.includes('web') || primaryIntent.includes('search')) {
      relevantTools.push(...tools.filter(t => t.category === 'Web'));
    }

    // Terminal operations
    if (primaryIntent.includes('run') || primaryIntent.includes('install') || primaryIntent.includes('command')) {
      relevantTools.push(...tools.filter(t => t.category === 'Terminal'));
    }

    // AI operations
    if (primaryIntent.includes('generate') || primaryIntent.includes('analyze') || primaryIntent.includes('code')) {
      relevantTools.push(...tools.filter(t => t.category === 'AI & Reasoning'));
    }

    // If no specific tools found, include all
    if (relevantTools.length === 0) {
      relevantTools = tools.slice(0, 10); // Limit to prevent overwhelming
    }

    return relevantTools;
  }

  // Execute the plan step by step
  async executePlan(plan) {
    const results = [];

    try {
      logger.info(`🚀 Executing plan with ${plan.steps.length} steps`);
      logger.info(`📊 Complexity: ${plan.complexity}, Estimated time: ${plan.estimatedTime}`);

      for (let i = 0; i < plan.steps.length; i++) {
        const step = plan.steps[i];
        logger.tool(step.tool, 'running');

        // Find and execute the tool
        const tool = tools.find(t => t.name === step.tool);

        if (!tool) {
          logger.error(`Tool not found: ${step.tool}`);
          continue;
        }

        try {
          const result = await tool.fn(step.parameters);

          if (result.success) {
            logger.tool(step.tool, 'success');
            logger.success(`Step ${i + 1}: ${step.purpose}`);
          } else {
            logger.tool(step.tool, 'failed');
            logger.error(`Step ${i + 1} failed: ${result.error}`);
          }

          results.push({
            step: i + 1,
            tool: step.tool,
            purpose: step.purpose,
            result,
            timestamp: Date.now()
          });

          // Store result in memory for context
          memorySystem.addContext(`step_${i}_result`, result, 'execution');

        } catch (error) {
          logger.tool(step.tool, 'failed');
          logger.error(`Step ${i + 1} error: ${error.message}`);

          results.push({
            step: i + 1,
            tool: step.tool,
            purpose: step.purpose,
            result: { success: false, error: error.message },
            timestamp: Date.now()
          });
        }
      }

      return {
        success: true,
        results,
        plan,
        executionTime: Date.now()
      };
    } catch (error) {
      logger.error('Plan execution failed:', error.message);
      return {
        success: false,
        error: error.message,
        results
      };
    }
  }

  // Generate final response based on execution results
  async generateFinalResponse(userInput, executionResults) {
    try {
      const responsePrompt = `
User Request: "${userInput}"
Execution Results: ${JSON.stringify(executionResults.results, null, 2)}

Based on the execution results, provide a comprehensive response to the user that:
1. Summarizes what was accomplished
2. Highlights any important findings or outputs
3. Suggests next steps if appropriate
4. Addresses any errors that occurred

Make the response conversational, helpful, and actionable.
`;

      const response = await streamHandler.streamResponse(responsePrompt, { showThinking: false });

      return response;
    } catch (error) {
      return {
        success: false,
        error: `Failed to generate final response: ${error.message}`
      };
    }
  }
}

const orchestrator = new SmartOrchestrator();

// Load memories and deployments
loadMemories();
loadDeployments();

// ═══════════════════════════════════════════════════════════════════════════════
// 🎨 ENHANCED INTERACTIVE MODE
// ═══════════════════════════════════════════════════════════════════════════════

async function interactiveMode() {
  // Display enhanced welcome message
  console.log(chalk.bold.cyan('\n╔══════════════════════════════════════════════════════════════╗'));
  console.log(chalk.bold.cyan('║') + chalk.bold.white('              🤖 Advanced AI Agent v2.0                    ') + chalk.bold.cyan('║'));
  console.log(chalk.bold.cyan('║') + chalk.white('         🚀 Enhanced with Smart Tool Orchestration          ') + chalk.bold.cyan('║'));
  console.log(chalk.bold.cyan('╚══════════════════════════════════════════════════════════════╝'));

  console.log(chalk.green('\n✨ Features:'));
  console.log(chalk.yellow('  • 🧠 Smart intent recognition and tool selection'));
  console.log(chalk.yellow('  • 🔄 Real-time streaming responses'));
  console.log(chalk.yellow('  • 🛠️  50+ integrated tools for development'));
  console.log(chalk.yellow('  • 💾 Advanced memory and context awareness'));
  console.log(chalk.yellow('  • 🎯 Multi-step task orchestration'));

  console.log(chalk.green('\n🎮 Commands:'));
  console.log(chalk.yellow('  • Type your request naturally - I\'ll understand and help!'));
  console.log(chalk.yellow('  • !clear - Clear conversation history'));
  console.log(chalk.yellow('  • !config - Show current configuration'));
  console.log(chalk.yellow('  • !tools - List available tools'));
  console.log(chalk.yellow('  • !memory - Show memory status'));
  console.log(chalk.yellow('  • !help - Show this help message'));
  console.log(chalk.yellow('  • exit - Quit the application'));

  console.log(chalk.gray('\n─'.repeat(60)));

  // Initialize AI and enhanced systems
  initializeAI();
  await memorySystem.loadMemory();
  const conversationManager = new ConversationManager();

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    prompt: config.userPrompt,
    historySize: 100
  });

  rl.prompt();

  rl.on('line', async (line) => {
    const input = line.trim();

    // Handle special commands
    if (input.toLowerCase() === 'exit') {
      console.log(chalk.green('\n👋 Saving memory and shutting down...'));
      await memorySystem.saveMemory();
      console.log(chalk.green('✅ Goodbye!'));
      rl.close();
      return;
    }

    if (input.toLowerCase() === '!clear') {
      conversationManager.clearHistory();
      memorySystem.conversationMemory = [];
      console.log(chalk.green('🧹 Conversation history and memory cleared.'));
      rl.prompt();
      return;
    }

    if (input.toLowerCase() === '!config') {
      console.log(chalk.cyan('\n⚙️  Current Configuration:'));
      const safeConfig = { ...config };
      if (safeConfig.apiKey) {
        safeConfig.apiKey = safeConfig.apiKey.substring(0, 8) + '...' + safeConfig.apiKey.substring(safeConfig.apiKey.length - 4);
      }
      console.log(JSON.stringify(safeConfig, null, 2));
      rl.prompt();
      return;
    }

    if (input.toLowerCase() === '!tools') {
      console.log(chalk.cyan('\n🛠️  Available Tools:'));
      const categories = [...new Set(tools.map(t => t.category))];
      categories.forEach(category => {
        console.log(chalk.bold.yellow(`\n📁 ${category}:`));
        tools.filter(t => t.category === category).forEach(tool => {
          console.log(chalk.white(`  • ${tool.name}: ${tool.description}`));
        });
      });
      rl.prompt();
      return;
    }

    if (input.toLowerCase() === '!memory') {
      console.log(chalk.cyan('\n🧠 Memory Status:'));
      console.log(chalk.white(`  • Conversation entries: ${memorySystem.conversationMemory.length}`));
      console.log(chalk.white(`  • Task memories: ${memorySystem.taskMemory.size}`));
      console.log(chalk.white(`  • Context entries: ${memorySystem.contextMemory.size}`));
      console.log(chalk.white(`  • Long-term memories: ${memorySystem.longTermMemory.size}`));
      rl.prompt();
      return;
    }

    if (input.toLowerCase() === '!help') {
      console.log(chalk.green('\n📖 Advanced AI Agent Help'));
      console.log(chalk.yellow('\n🎯 How to use:'));
      console.log(chalk.white('  • Just type what you want to do naturally!'));
      console.log(chalk.white('  • I can create files, run commands, search the web, generate code, and much more'));
      console.log(chalk.white('  • I understand context and can handle multi-step tasks'));
      console.log(chalk.yellow('\n🎮 Special commands:'));
      console.log(chalk.white('  • !clear - Clear conversation history'));
      console.log(chalk.white('  • !config - Show current configuration'));
      console.log(chalk.white('  • !tools - List all available tools'));
      console.log(chalk.white('  • !memory - Show memory status'));
      console.log(chalk.white('  • !help - Show this help message'));
      console.log(chalk.white('  • exit - Exit the application'));
      rl.prompt();
      return;
    }

    if (!input) {
      rl.prompt();
      return;
    }

    // Add user message to memory and history
    memorySystem.addConversationMemory('user', input, { timestamp: Date.now() });
    conversationManager.addMessage('user', input);

    try {
      console.log(chalk.gray('\n─'.repeat(60)));

      // Create execution plan using smart orchestrator
      logger.thinking('Analyzing your request and creating execution plan...');
      const plan = await orchestrator.createExecutionPlan(input);

      if (!plan) {
        throw new Error('Failed to create execution plan');
      }

      // Execute the plan
      const executionResults = await orchestrator.executePlan(plan);

      if (!executionResults.success) {
        throw new Error(`Execution failed: ${executionResults.error}`);
      }

      // Generate final response
      logger.thinking('Generating comprehensive response...');
      const finalResponse = await orchestrator.generateFinalResponse(input, executionResults);

      if (finalResponse.success) {
        // Add response to memory and history
        memorySystem.addConversationMemory('assistant', finalResponse.response, {
          timestamp: Date.now(),
          toolsUsed: executionResults.results.map(r => r.tool),
          planComplexity: plan.complexity
        });
        conversationManager.addMessage('assistant', finalResponse.response);

        console.log(chalk.gray('\n─'.repeat(60)));
        logger.success('Task completed successfully!');
      } else {
        throw new Error(`Response generation failed: ${finalResponse.error}`);
      }

2. run_command - Execute a shell command
   - commandLine: (REQUIRED) the command to execute
   - cwd: working directory (optional, defaults to current directory)
   - blocking: whether to wait for command completion (optional, defaults to true)
   - waitMsBeforeAsync: milliseconds to wait before returning for async commands
   - safeToAutoRun: whether the command is safe to run automatically

3. grep_search - Search for text in files
   - searchPath: (REQUIRED) directory to search in
   - query: (REQUIRED) text to search for
   - includes: array of file patterns to include (optional)
   - caseInsensitive: whether to ignore case (optional)
   - matchPerLine: limit matches per line (optional)

4. list_dir - List directory contents
   - directoryPath: (REQUIRED) directory to list

5. read_url_content - Fetch content from a URL
   - url: (REQUIRED) URL to fetch content from

6. search_web - Search the web for information
   - query: (REQUIRED) search query
   - domain: specific domain to search within (optional)

7. browser_preview - Open a browser preview for a web application
   - url: (REQUIRED) URL to open
   - name: (REQUIRED) name for the preview

8. deploy_web_app - Deploy a web application
   - framework: (REQUIRED) framework used (React, Vue, Angular, etc.)
   - projectPath: (REQUIRED) path to the project
   - subdomain: custom subdomain for deployment
   - projectId: unique ID for the project

9. check_deploy_status - Check deployment status
   - deploymentId: (REQUIRED) ID of the deployment to check

10. create_memory - Save important context
    - id: (REQUIRED) unique identifier for the memory
    - title: (REQUIRED) title of the memory
    - content: (REQUIRED) content to remember
    - tags: array of tags for categorization
    - corpusNames: array of corpus names
    - action: action to take (create, update, delete)
    - userTriggered: whether the user triggered this

11. find_by_name - Find files by name
    - searchDirectory: (REQUIRED) directory to search in
    - pattern: (REQUIRED) pattern to search for
    - excludes: patterns to exclude
    - type: type of items to find (file, directory, etc.)
    - maxDepth: maximum depth to search
    - extensions: file extensions to include
    - fullPath: whether to return full paths

12. view_file - View file contents
    - absolutePath: (REQUIRED) path to the file
    - startLine: first line to show
    - endLine: last line to show
    - includeSummaryOfOtherLines: whether to include a summary of other lines

13. write_to_file - Create a new file
    - targetFile: (REQUIRED) path to the file to create
    - codeContent: (REQUIRED) content to write
    - emptyFile: whether to create an empty file

14. codebase_search - Search for code snippets
    - query: (REQUIRED) search query
    - targetDirectories: directories to search in

15. command_status - Check command status
    - commandId: (REQUIRED) ID of the command to check
    - outputPriority: priority of output to show
    - outputCharacterCount: maximum characters to show
    - waitDurationSeconds: seconds to wait for completion

16. read_deployment_config - Read deployment configuration
    - projectPath: (REQUIRED) path to the project

17. view_code_item - View a specific code item
    - nodePath: (REQUIRED) path to the code item

18. view_web_document_content_chunk - View a chunk of web content
    - url: (REQUIRED) URL of the web document
    - position: position of the chunk to view

19. suggested_responses - Suggest responses to the user
    - suggestions: (REQUIRED) array of suggested responses

20. analyze_code - Analyze code for patterns, bugs, and improvements
    - codePath: (REQUIRED) path to the code file
    - language: programming language of the code
    - analysisType: type of analysis (security, performance, style)

21. generate_project - Generate a complete project structure
    - projectName: (REQUIRED) name of the project
    - framework: framework to use (React, Vue, Angular, etc.)
    - language: programming language (JavaScript, TypeScript, etc.)
    - features: array of features to include

22. install_dependencies - Install project dependencies
    - packageManager: package manager to use (npm, yarn, pip, etc.)
    - dependencies: array of dependencies to install
    - projectPath: path to the project
    - dev: whether to install as dev dependencies

23. database_operation - Perform database operations
    - operation: (REQUIRED) operation to perform (create, read, update, delete)
    - dbType: database type (MongoDB, MySQL, PostgreSQL, etc.)
    - connectionString: connection string for the database
    - query: query to execute

24. api_integration - Integrate with external APIs
    - apiName: (REQUIRED) name of the API
    - endpoint: endpoint to call
    - method: HTTP method
    - headers: headers to include
    - body: request body

25. deploy_backend - Deploy a backend service
    - servicePath: (REQUIRED) path to the service
    - platform: platform to deploy to (AWS, GCP, Azure, etc.)
    - region: region to deploy to
    - environment: environment to deploy to (dev, staging, prod)

26. setup_ci_cd - Set up CI/CD pipeline
    - provider: (REQUIRED) CI/CD provider (GitHub Actions, Jenkins, etc.)
    - projectPath: path to the project
    - steps: array of pipeline steps

27. analyze_performance - Analyze application performance
    - targetUrl: (REQUIRED) URL to analyze
    - metrics: array of metrics to analyze
    - duration: duration of the analysis

    28. generate_documentation - Generate documentation
    - sourcePath: (REQUIRED) path to the source code
    - outputFormat: format of the documentation (markdown, HTML, etc.)
    - outputPath: path to output the documentation
   38. natural_language_processing - Process and analyze text
      - text: (REQUIRED) text to analyze
      - operation: operation to perform (sentiment, entities, summarization, etc.)
      - language: language of the text

Respond with a structured plan that includes:
1. The user's intention
2. Which tool(s) to use
3. Parameters for each tool
4. Expected outcome

Format your response as valid JSON with the following structure:
{
  "intention": "user's intention",
  "steps": [
    {
      "tool": "tool_name",
      "parameters": { param_object },
      "purpose": "why this step is needed"
    }
  ],
  "expectedOutcome": "what the user will get"
}
`;

      const planResult = await model.generateContent(planPrompt);
      const planText = planResult.response.text();

      // Extract the JSON plan
      const jsonMatch = planText.match(/```json\n([\s\S]*?)\n```/) || planText.match(/({[\s\S]*})/);
      let plan;

      if (jsonMatch) {
        try {
          plan = JSON.parse(jsonMatch[1].trim());
        } catch (e) {
          try {
          plan = JSON.parse(planText);
          } catch (e2) {
            throw new Error("Couldn't parse the plan as valid JSON");
          }
        }
      } else {
        throw new Error("Couldn't parse the plan");
      }

      // Validate plan structure
      if (!plan.intention || !Array.isArray(plan.steps)) {
        throw new Error("Invalid plan structure: missing 'intention' or 'steps' array");
      }

      spinner.succeed('Plan created');
      console.log(chalk.green('\nUnderstanding your request:'));
      console.log(chalk.yellow(plan.intention));

      // Execute each step in the plan
      for (const [index, step] of plan.steps.entries()) {
        console.log(chalk.green(`\nStep ${index + 1}: ${step.purpose || 'Executing next step'}`));

        // Validate step structure
        if (!step.tool) {
          console.log(chalk.red(`Step ${index + 1} is missing the 'tool' field. Skipping.`));
          continue;
        }

        if (!step.parameters) {
          console.log(chalk.red(`Step ${index + 1} is missing the 'parameters' field. Skipping.`));
          continue;
        }

        spinner.start(`Executing: ${step.tool}`);

        // Execute the appropriate tool
        let result;
        switch (step.tool) {
          case 'edit_file':
            result = await executeEditFile(step.parameters);
            break;
          case 'run_command':
            result = await executeRunCommand(step.parameters);
            break;
          case 'grep_search':
            result = await executeGrepSearch(step.parameters);
            break;
          case 'list_dir':
            result = await executeListDir(step.parameters);
            break;
          case 'read_url_content':
            result = await executeReadUrlContent(step.parameters);
            break;
          case 'search_web':
            result = await executeSearchWeb(step.parameters);
            break;
          case 'browser_preview':
            result = await executeBrowserPreview(step.parameters);
            break;
          case 'deploy_web_app':
            result = await executeDeployWebApp(step.parameters);
            break;
          case 'check_deploy_status':
            result = await executeCheckDeployStatus(step.parameters);
            break;
          case 'create_memory':
            result = await executeCreateMemory(step.parameters);
            break;
          case 'find_by_name':
            result = await executeFindByName(step.parameters);
            break;
          case 'view_file':
            result = await executeViewFile(step.parameters);
            break;
          case 'write_to_file':
            result = await executeWriteToFile(step.parameters);
            break;
          case 'codebase_search':
            result = await executeCodebaseSearch(step.parameters);
            break;
          case 'command_status':
            result = await executeCommandStatus(step.parameters);
            break;
          case 'read_deployment_config':
            result = await executeReadDeploymentConfig(step.parameters);
            break;
          case 'view_code_item':
            result = await executeViewCodeItem(step.parameters);
            break;
          case 'view_web_document_content_chunk':
            result = await executeViewWebDocumentContentChunk(step.parameters);
            break;
          case 'suggested_responses':
            result = await executeSuggestedResponses(step.parameters);
            break;
          case 'analyze_code':
            result = await executeAnalyzeCode(step.parameters);
            break;
          case 'generate_project':
            result = await executeGenerateProject(step.parameters);
            break;
          case 'install_dependencies':
            result = await executeInstallDependencies(step.parameters);
            break;
          case 'database_operation':
            result = await executeDatabaseOperation(step.parameters);
            break;
          case 'api_integration':
            result = await executeApiIntegration(step.parameters);
            break;
          case 'deploy_backend':
            result = await executeDeployBackend(step.parameters);
            break;
          case 'setup_ci_cd':
            result = await executeSetupCiCd(step.parameters);
            break;
          case 'analyze_performance':
            result = await executeAnalyzePerformance(step.parameters);
            break;
          case 'generate_documentation':
            result = await executeGenerateDocumentation(step.parameters);
            break;
          case 'natural_language_processing':
            result = await executeNaturalLanguageProcessing(step.parameters);
            break;
          default:
            result = { success: false, error: `Unknown tool: ${step.tool}` };
        }

        if (result.success) {
          spinner.succeed(`${step.tool} completed successfully`);
          console.log(chalk.cyan('Result:'), result.message || JSON.stringify(result, null, 2));
        } else {
          spinner.fail(`${step.tool} failed: ${result.error}`);
        }

        // Add tool result to history for context
        conversationManager.addMessage('assistant', `Tool ${step.tool} result: ${JSON.stringify(result)}`);
      }

      // Generate a final response
      spinner.start('Generating final response...');

      const responsePrompt = `
Based on the user's request "${input}" and the results of the tools execution, provide a final response that summarizes what was done and the outcome.
Make the response conversational and helpful. If there were any errors, suggest how to fix them.
If the user is trying to build a project, provide guidance on next steps.
`;

      // Get the conversation history
      const formattedHistory = conversationManager.getFormattedHistory();

      // If history is empty, just use the response prompt
      if (formattedHistory.length === 0) {
        const responseResult = await model.generateContent(responsePrompt);
        const response = responseResult.response.text();

        spinner.succeed('Done');
        console.log(chalk.green('\nAgent Response:'));
        console.log(response);

        // Add response to history
        conversationManager.addMessage('assistant', response);
      } else {
        // Add the response prompt as a user message at the beginning
        const chatWithPrompt = [
          { role: 'user', parts: [{ text: responsePrompt }] },
          ...formattedHistory
        ];

        try {
          // Generate response with conversation history
          const responseResult = await model.generateContent({ contents: chatWithPrompt });
          const response = responseResult.response.text();

          spinner.succeed('Done');
          console.log(chalk.green('\nAgent Response:'));
          console.log(response);

          // Add response to history
          conversationManager.addMessage('assistant', response);
        } catch (error) {
          // If the chat history approach fails, fall back to just the prompt
          console.log(chalk.yellow('Falling back to simpler response generation...'));
          const fallbackResult = await model.generateContent(responsePrompt);
          const fallbackResponse = fallbackResult.response.text();

          spinner.succeed('Done (fallback)');
          console.log(chalk.green('\nAgent Response:'));
          console.log(fallbackResponse);

          // Add fallback response to history
          conversationManager.addMessage('assistant', fallbackResponse);
        }
      }
    } catch (error) {
      spinner.fail('Error occurred');
      console.error(chalk.red('Error:'), error.message);
    }

    console.log('\n');
    rl.prompt();
  });
}

// Tool execution functions
async function executeEditFile({ targetFile, instruction, content, codeEdit, codeMarkdownLanguage, targetLintErrorIds }) {
  try {
    // Check if targetFile is defined
    if (!targetFile) {
      return {
        success: false,
        error: 'Target file path is required but was not provided'
      };
    }

    // If file doesn't exist and content is provided, create it
    if (!fs.existsSync(targetFile) && content) {
      const dirPath = path.dirname(targetFile);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
      fs.writeFileSync(targetFile, content, 'utf8');
      return {
        success: true,
        message: `File ${targetFile} created successfully`
      };
    }

    // If file doesn't exist and no content but there's an instruction, generate content
    if (!fs.existsSync(targetFile) && !content && instruction) {
      const dirPath = path.dirname(targetFile);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }

      // Generate content using AI based on the instruction
      const createPrompt = `
You are an AI code generator. Create a new file according to these instructions:
${instruction}

The file path is: ${targetFile}
${codeMarkdownLanguage ? `The language is: ${codeMarkdownLanguage}` : ''}

Respond with ONLY the file content, without any explanations or markdown formatting.
`;

      try {
        const result = await model.generateContent(createPrompt);
        const generatedContent = result.response.text();

        if (!generatedContent || generatedContent.trim() === '') {
          throw new Error('Generated content is empty');
        }

        fs.writeFileSync(targetFile, generatedContent, 'utf8');
        return {
          success: true,
          message: `File ${targetFile} created successfully based on instructions`
        };
      } catch (error) {
        return {
          success: false,
          error: `Failed to generate content: ${error.message}`
        };
      }
    }

    // If file exists, edit it
    if (fs.existsSync(targetFile)) {
      const currentContent = fs.readFileSync(targetFile, 'utf8');

      if (!instruction && !codeEdit) {
        return {
          success: false,
          error: 'Instruction or codeEdit is required for editing an existing file'
        };
      }

      // If specific code edit is provided
      if (codeEdit) {
        // Handle specific code edits
        // This could be replacing specific lines, adding imports, etc.
        let editedContent = currentContent;

        if (codeEdit.replace) {
          const { pattern, replacement } = codeEdit.replace;
          editedContent = currentContent.replace(new RegExp(pattern, 'g'), replacement);
        } else if (codeEdit.addImport) {
          // Add import statement at the top of the file
          const importStatement = codeEdit.addImport;
          if (!currentContent.includes(importStatement)) {
            editedContent = importStatement + '\n' + currentContent;
          }
        } else if (codeEdit.insertAt) {
          // Insert content at specific line
          const { line, content } = codeEdit.insertAt;
          const lines = currentContent.split('\n');
          lines.splice(line - 1, 0, content);
          editedContent = lines.join('\n');
        }

        fs.writeFileSync(targetFile, editedContent, 'utf8');
        return {
          success: true,
          message: `File ${targetFile} edited with specific code changes`
        };
      }

      // If lint error IDs are provided, focus on fixing those
      let lintContext = '';
      if (targetLintErrorIds && targetLintErrorIds.length > 0) {
        lintContext = `
Focus on fixing these specific lint errors: ${targetLintErrorIds.join(', ')}
`;
      }

      // Generate edited content using AI
      const editPrompt = `
You are an AI code editor. Edit the following file according to these instructions:
${instruction}
${lintContext}

Here is the current file content:
\`\`\`${codeMarkdownLanguage || ''}
${currentContent}
\`\`\`

Respond with ONLY the new file content, without any explanations or markdown formatting.
`;

      try {
      const result = await model.generateContent(editPrompt);
      const editedContent = result.response.text();

        if (!editedContent || editedContent.trim() === '') {
          throw new Error('Generated content is empty');
        }

      fs.writeFileSync(targetFile, editedContent, 'utf8');
      return {
        success: true,
        message: `File ${targetFile} edited according to instructions`
      };
      } catch (error) {
        return {
          success: false,
          error: `Failed to generate edited content: ${error.message}`
        };
      }
    }

    // If file doesn't exist and no content, error
    return {
      success: false,
      error: `File ${targetFile} does not exist and no content provided for creation`
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * Execute a shell command
 * @param {Object} options - Command execution options
 * @param {string} options.commandLine - The command to execute
 * @param {string} options.cwd - Working directory for the command
 * @param {boolean} options.blocking - Whether to wait for the command to complete
 * @param {number} options.waitMsBeforeAsync - Milliseconds to wait before returning for async commands
 * @param {boolean} options.safeToAutoRun - Whether the command is safe to run automatically
 * @returns {Object} - Command execution result
 */
async function executeRunCommand({ commandLine, cwd = process.cwd(), blocking = true, waitMsBeforeAsync = 500, safeToAutoRun = true }) {
  const commandId = uuidv4();

  // Log the command being executed
  logger.debug(`Executing command: ${commandLine}`);
  logger.debug(`Working directory: ${cwd}`);
  logger.debug(`Blocking: ${blocking}`);

  try {
    if (!fs.existsSync(cwd)) {
      return { success: false, error: `Working directory does not exist: ${cwd}` };
    }

    // Adapt command for Windows if needed
    let adaptedCommand = commandLine;
    if (IS_WINDOWS) {
      // Check if it's a simple command that needs to be prefixed with powershell
      if (!commandLine.toLowerCase().startsWith('powershell') &&
          !commandLine.toLowerCase().includes('.exe') &&
          !commandLine.toLowerCase().includes('.cmd') &&
          !commandLine.toLowerCase().includes('.bat')) {

        // Escape double quotes for PowerShell
        const escapedCommand = commandLine.replace(/"/g, '`"');
        adaptedCommand = `powershell -Command "${escapedCommand}"`;
        logger.debug(`Adapted command for Windows: ${adaptedCommand}`);
      }
    }

    // Store command in running commands
    runningCommands[commandId] = {
      commandLine: adaptedCommand,
      originalCommand: commandLine,
      cwd,
      status: 'running',
      startTime: new Date().toISOString(),
      stdout: '',
      stderr: ''
    };

    if (blocking) {
      // Run command and wait for completion
      const execOptions = {
        cwd,
        maxBuffer: 10 * 1024 * 1024, // 10MB buffer for large outputs
        timeout: config.defaultTimeout || 60000 // Default timeout from config
      };

      const { stdout, stderr } = await execAsync(adaptedCommand, execOptions);

      // Update command status
      runningCommands[commandId].status = 'completed';
      runningCommands[commandId].stdout = stdout;
      runningCommands[commandId].stderr = stderr;
      runningCommands[commandId].endTime = new Date().toISOString();

      // Move to command history
      commandHistory[commandId] = { ...runningCommands[commandId] };
      delete runningCommands[commandId];

      return {
        success: true,
        commandId,
        stdout,
        stderr,
        message: `Command executed: ${commandLine}`
      };
    } else {
      // Run command asynchronously
      if (waitMsBeforeAsync > 0) {
        await new Promise(resolve => setTimeout(resolve, waitMsBeforeAsync));
      }

      // For Windows, ensure we use the right shell
      const spawnOptions = {
        cwd,
        shell: IS_WINDOWS ? 'powershell.exe' : true,
        stdio: ['ignore', 'pipe', 'pipe']
      };

      // For Windows, we need to handle the command differently
      let childProcess;
      if (IS_WINDOWS && adaptedCommand.startsWith('powershell')) {
        // Extract the actual command from the powershell wrapper
        const psCommand = adaptedCommand.replace(/^powershell -Command "(.*)"$/, '$1');
        childProcess = spawn('powershell', ['-Command', psCommand], spawnOptions);
      } else {
        childProcess = spawn(adaptedCommand, [], spawnOptions);
      }

      // Store child process reference
      runningCommands[commandId].process = childProcess;

      // Capture stdout and stderr
      childProcess.stdout.on('data', (data) => {
        const output = data.toString();
        runningCommands[commandId].stdout += output;
        if (config.debug) {
          logger.debug(`[${commandId}] stdout: ${output}`);
        }
      });

      childProcess.stderr.on('data', (data) => {
        const output = data.toString();
        runningCommands[commandId].stderr += output;
        if (config.debug) {
          logger.debug(`[${commandId}] stderr: ${output}`);
        }
      });

      // Handle process completion
      childProcess.on('close', (code) => {
        runningCommands[commandId].status = code === 0 ? 'completed' : 'failed';
        runningCommands[commandId].exitCode = code;
        runningCommands[commandId].endTime = new Date().toISOString();

        logger.debug(`Command ${commandId} completed with exit code ${code}`);

        // Move to command history
        commandHistory[commandId] = { ...runningCommands[commandId] };
        delete runningCommands[commandId];
      });

      return {
        success: true,
        commandId,
        message: `Command started asynchronously: ${commandLine}`,
        async: true
      };
    }
  } catch (error) {
    // Handle any errors that occurred during command execution
    logger.error(`Command execution error: ${error.message}`);

    if (runningCommands[commandId]) {
      runningCommands[commandId].status = 'failed';
      runningCommands[commandId].error = error.message;
      runningCommands[commandId].endTime = new Date().toISOString();

      // Move to command history
      commandHistory[commandId] = { ...runningCommands[commandId] };
      delete runningCommands[commandId];
    }

    return {
      success: false,
      commandId,
      error: error.message
    };
  }
}

/**
 * Execute the command status tool
 * @param {Object} parameters - Parameters for checking command status
 * @returns {Object} - Command status information
 */
async function executeCommandStatus(parameters) {
  const { commandId } = parameters;

  if (!commandId) {
    return { success: false, error: 'Command ID is required' };
  }

  // Check if command is still running
  if (runningCommands[commandId]) {
      return {
      success: true,
      status: runningCommands[commandId].status,
      stdout: runningCommands[commandId].stdout,
      stderr: runningCommands[commandId].stderr,
      startTime: runningCommands[commandId].startTime,
      running: true
    };
  }

  // Check if command is in history
  if (commandHistory[commandId]) {
    return {
      success: true,
      status: commandHistory[commandId].status,
      stdout: commandHistory[commandId].stdout,
      stderr: commandHistory[commandId].stderr,
      startTime: commandHistory[commandId].startTime,
      endTime: commandHistory[commandId].endTime,
      exitCode: commandHistory[commandId].exitCode,
      running: false
    };
  }

  return { success: false, error: `Command with ID ${commandId} not found` };
}

/**
 * Execute the containerize app tool
 * @param {Object} parameters - Parameters for containerizing the application
 * @returns {Object} - Result of containerization
 */
async function executeContainerizeApp(parameters) {
  const { appPath, dockerfilePath, imageName, buildArgs } = parameters;

  if (!appPath) {
    return { success: false, error: 'Application path is required' };
  }

  if (!imageName) {
    return { success: false, error: 'Image name is required' };
    }

    try {
    // Check if app path exists
    if (!fs.existsSync(appPath)) {
      return { success: false, error: `Application path does not exist: ${appPath}` };
    }

    // If Dockerfile is provided, check if it exists
    if (dockerfilePath && !fs.existsSync(dockerfilePath)) {
      return { success: false, error: `Dockerfile does not exist: ${dockerfilePath}` };
    }

    // Use provided Dockerfile or create one if not provided
    let dockerfile = dockerfilePath;
    if (!dockerfile) {
      dockerfile = path.join(appPath, 'Dockerfile');

      // Create a basic Dockerfile if it doesn't exist
      if (!fs.existsSync(dockerfile)) {
        const basicDockerfile = `FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "start"]`;

        fs.writeFileSync(dockerfile, basicDockerfile);
        console.log(chalk.green('Created a basic Dockerfile'));
      }
    }

    // Build the Docker image
    const buildArgsString = buildArgs ? Object.entries(buildArgs)
      .map(([key, value]) => `--build-arg ${key}=${value}`)
      .join(' ') : '';

    const buildCommand = `docker build ${buildArgsString} -t ${imageName} -f ${dockerfile} ${appPath}`;

    const result = await executeRunCommand({
      commandLine: buildCommand,
      cwd: process.cwd(),
      blocking: true
    });

    if (result.success) {
      return {
        success: true,
        imageName,
        message: `Successfully built Docker image: ${imageName}`,
        dockerfilePath: dockerfile,
        buildOutput: result.stdout
      };
    } else {
        return {
        success: false,
        error: `Failed to build Docker image: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error containerizing app: ${error.message}`
    };
  }
}

/**
 * Execute the run container tool
 * @param {Object} parameters - Parameters for running the container
 * @returns {Object} - Result of container execution
 */
async function executeRunContainer(parameters) {
  const { imageName, containerName, ports, envVars, volumes, detached } = parameters;

  if (!imageName) {
    return { success: false, error: 'Image name is required' };
  }

  try {
    // Build the docker run command
    let runCommand = 'docker run';

    // Add container name if provided
    if (containerName) {
      runCommand += ` --name ${containerName}`;
    }

    // Add port mappings if provided
    if (ports && Array.isArray(ports)) {
      ports.forEach(port => {
        runCommand += ` -p ${port}`;
      });
    }

    // Add environment variables if provided
    if (envVars && typeof envVars === 'object') {
      Object.entries(envVars).forEach(([key, value]) => {
        runCommand += ` -e ${key}=${value}`;
      });
    }

    // Add volume mappings if provided
    if (volumes && Array.isArray(volumes)) {
      volumes.forEach(volume => {
        runCommand += ` -v ${volume}`;
      });
    }
    // Add detached flag if specified
    if (detached) {
      runCommand += ' -d';
    }

    // Add the image name
    runCommand += ` ${imageName}`;

    // Execute the command
    const result = await executeRunCommand({
      commandLine: runCommand,
      cwd: process.cwd(),
      blocking: !detached
    });

    if (result.success) {
      return {
        success: true,
        imageName,
        containerName: containerName || 'unnamed',
        message: `Successfully started container from image: ${imageName}`,
        commandOutput: result.stdout,
        detached: !!detached,
        commandId: result.commandId
      };
    } else {
      return {
        success: false,
        error: `Failed to run container: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
      return {
        success: false,
      error: `Error running container: ${error.message}`
    };
  }
}

/**
 * Execute the list containers tool
 * @param {Object} parameters - Parameters for listing containers
 * @returns {Object} - List of containers
 */
async function executeListContainers(parameters) {
  const { all } = parameters || {};

  try {
    const command = all ? 'docker ps -a' : 'docker ps';

    const result = await executeRunCommand({
      commandLine: command,
      cwd: process.cwd(),
      blocking: true
    });

    if (result.success) {
      // Parse the output to extract container information
      const lines = result.stdout.trim().split('\n');

      if (lines.length <= 1) {
      return {
          success: true,
          containers: [],
          message: 'No containers found'
        };
      }

      // Skip the header line and parse container info
      const containers = lines.slice(1).map(line => {
        const parts = line.trim().split(/\s+/);
        const containerId = parts[0];
        const image = parts[1];
        const status = parts.slice(4, -1).join(' ');
        const name = parts[parts.length - 1];

        return { containerId, image, status, name };
    });

    return {
      success: true,
        containers,
        count: containers.length,
        message: `Found ${containers.length} container(s)`
      };
    } else {
      return {
        success: false,
        error: `Failed to list containers: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error listing containers: ${error.message}`
    };
  }
}

/**
 * Execute the stop container tool
 * @param {Object} parameters - Parameters for stopping the container
 * @returns {Object} - Result of container stop operation
 */
async function executeStopContainer(parameters) {
  const { containerId, containerName } = parameters;

  if (!containerId && !containerName) {
    return { success: false, error: 'Container ID or name is required' };
  }

  try {
    const target = containerId || containerName;
    const command = `docker stop ${target}`;

    const result = await executeRunCommand({
      commandLine: command,
      cwd: process.cwd(),
      blocking: true
    });

    if (result.success) {
    return {
      success: true,
        containerId: target,
        message: `Successfully stopped container: ${target}`,
        output: result.stdout.trim()
      };
    } else {
      return {
        success: false,
        error: `Failed to stop container: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error stopping container: ${error.message}`
    };
  }
}

/**
 * Execute the remove container tool
 * @param {Object} parameters - Parameters for removing the container
 * @returns {Object} - Result of container removal operation
 */
async function executeRemoveContainer(parameters) {
  const { containerId, containerName, force } = parameters;

  if (!containerId && !containerName) {
    return { success: false, error: 'Container ID or name is required' };
  }

  try {
    const target = containerId || containerName;
    let command = `docker rm ${target}`;

    if (force) {
      command += ' -f';
    }

    const result = await executeRunCommand({
      commandLine: command,
      cwd: process.cwd(),
      blocking: true
    });

    if (result.success) {
      return {
        success: true,
        containerId: target,
        message: `Successfully removed container: ${target}`,
        output: result.stdout.trim()
      };
    } else {
      return {
        success: false,
        error: `Failed to remove container: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error removing container: ${error.message}`
    };
  }
}

/**
 * Execute the list images tool
 * @returns {Object} - List of Docker images
 */
async function executeListImages() {
  try {
    const command = 'docker images';

    const result = await executeRunCommand({
      commandLine: command,
      cwd: process.cwd(),
      blocking: true
    });

    if (result.success) {
      // Parse the output to extract image information
      const lines = result.stdout.trim().split('\n');

      if (lines.length <= 1) {
        return {
          success: true,
          images: [],
          message: 'No images found'
        };
      }

      // Skip the header line and parse image info
      const images = lines.slice(1).map(line => {
        const parts = line.trim().split(/\s+/);
        const repository = parts[0];
        const tag = parts[1];
        const imageId = parts[2];
        const created = parts[3] + ' ' + parts[4];
        const size = parts[parts.length - 1];

        return { repository, tag, imageId, created, size };
      });

      return {
        success: true,
        images,
        count: images.length,
        message: `Found ${images.length} image(s)`
      };
    } else {
      return {
        success: false,
        error: `Failed to list images: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error listing images: ${error.message}`
    };
  }
}

/**
 * Execute the remove image tool
 * @param {Object} parameters - Parameters for removing the image
 * @returns {Object} - Result of image removal operation
 */
async function executeRemoveImage(parameters) {
  const { imageId, imageName, force } = parameters;

  if (!imageId && !imageName) {
    return { success: false, error: 'Image ID or name is required' };
  }

  try {
    const target = imageId || imageName;
    let command = `docker rmi ${target}`;

    if (force) {
      command += ' -f';
    }

    const result = await executeRunCommand({
      commandLine: command,
      cwd: process.cwd(),
      blocking: true
    });

    if (result.success) {
      return {
        success: true,
        imageId: target,
        message: `Successfully removed image: ${target}`,
        output: result.stdout.trim()
      };
    } else {
      return {
        success: false,
        error: `Failed to remove image: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error removing image: ${error.message}`
    };
  }
}

async function executeGrepSearch({ searchPath, query, includes = [], caseInsensitive = false, matchPerLine }) {
  try {
    if (!searchPath) {
      return { success: false, error: 'Search path is required' };
    }

    if (!query) {
      return { success: false, error: 'Search query is required' };
    }

    // Check if search path exists
    if (!fs.existsSync(searchPath)) {
      return { success: false, error: `Search path does not exist: ${searchPath}` };
    }

    // Build grep command
    let command = `grep -r "${query}" ${searchPath}`;

    // Add case insensitive flag if specified
    if (caseInsensitive) {
      command += ' -i';
    }

    // Add includes if specified
    if (includes && includes.length > 0) {
      const includesArg = includes.map(pattern => `--include="${pattern}"`).join(' ');
      command += ` ${includesArg}`;
    }

    // Add match per line if specified
    if (matchPerLine) {
      command += ` -m ${matchPerLine}`;
    }

    try {
      const { stdout } = await execAsync(command);

      // Parse results
      const results = stdout.trim().split('\n').filter(Boolean);

      return {
        success: true,
        message: `Found ${results.length} matches for "${query}"`,
        results
      };
    } catch (error) {
      // grep returns exit code 1 if no matches found, which is not an error for us
      if (error.code === 1) {
        return {
          success: true,
          message: `No matches found for "${query}"`,
          results: []
        };
      }

      throw error;
    }
  } catch (error) {
    return {
      success: false,
      error: `Error during grep search: ${error.message}`
    };
  }
}

async function executeListDir({ directoryPath }) {
  try {
    if (!directoryPath) {
      return { success: false, error: 'Directory path is required' };
    }

    // Check if directory exists
    if (!fs.existsSync(directoryPath)) {
      return { success: false, error: `Directory does not exist: ${directoryPath}` };
    }

    // Check if it's actually a directory
    const stats = fs.statSync(directoryPath);
    if (!stats.isDirectory()) {
      return { success: false, error: `Not a directory: ${directoryPath}` };
    }

    // Read directory contents
    const contents = fs.readdirSync(directoryPath);

    // Get detailed information for each item
    const items = contents.map(item => {
      const itemPath = path.join(directoryPath, item);
      const itemStats = fs.statSync(itemPath);

      return {
        name: item,
        path: itemPath,
        isDirectory: itemStats.isDirectory(),
        size: itemStats.size,
        modified: itemStats.mtime.toISOString()
      };
    });

    // Separate directories and files
    const directories = items.filter(item => item.isDirectory);
    const files = items.filter(item => !item.isDirectory);

    return {
      success: true,
      message: `Listed ${items.length} items in ${directoryPath}`,
      items,
      directories,
      files,
      directoryPath
    };
  } catch (error) {
    return {
      success: false,
      error: `Error listing directory: ${error.message}`
    };
  }
}

async function executeReadUrlContent({ url }) {
  try {
    if (!url) {
      return { success: false, error: 'URL is required' };
    }

    // Validate URL
    try {
      new URL(url);
    } catch (e) {
      return { success: false, error: `Invalid URL: ${url}` };
    }

    // Fetch content
    const response = await axios.get(url);

    // Check if content is HTML
    const contentType = response.headers['content-type'] || '';
    const isHtml = contentType.includes('text/html');

    if (isHtml) {
      // Parse HTML content for better readability
      const $ = cheerio.load(response.data);

      // Remove scripts and styles for cleaner content
      $('script').remove();
      $('style').remove();

      // Get page title
      const title = $('title').text();

      // Get main content (trying to find the main content area)
      let mainContent = '';
      const mainElement = $('main').html() || $('article').html() || $('body').html();

      if (mainElement) {
        mainContent = cheerio.load(mainElement).text().trim();
      } else {
        mainContent = $('body').text().trim();
      }

      // Clean up the text
      mainContent = mainContent
        .replace(/\s+/g, ' ')
        .replace(/\n+/g, '\n')
        .trim();

      return {
        success: true,
        message: `Successfully fetched content from ${url}`,
        title,
        content: mainContent,
        url,
        isHtml: true
      };
    } else {
      // Handle non-HTML content
      let content = '';

      if (typeof response.data === 'string') {
        content = response.data;
      } else {
        content = JSON.stringify(response.data, null, 2);
      }

      return {
        success: true,
        message: `Successfully fetched content from ${url}`,
        content,
        contentType,
        url,
        isHtml: false
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error fetching URL content: ${error.message}`
    };
  }
}

async function executeSearchWeb({ query, domain }) {
  try {
    if (!query) {
      return { success: false, error: 'Search query is required' };
    }

    spinner.start('Searching the web...');

    // Launch browser in headless mode
    const browser = await puppeteer.launch({ headless: 'new' });
    const page = await browser.newPage();

    // Build search URL
    let searchUrl = `https://www.google.com/search?q=${encodeURIComponent(query)}`;

    // Add site: operator if domain is specified
    if (domain) {
      searchUrl = `https://www.google.com/search?q=site:${domain}+${encodeURIComponent(query)}`;
    }

    // Navigate to search page
    await page.goto(searchUrl, { waitUntil: 'domcontentloaded' });

    // Extract search results
    const results = await page.evaluate(() => {
      const searchResults = [];

      // Get all search result elements
      const resultElements = document.querySelectorAll('div.g');

      for (const element of resultElements) {
        const titleElement = element.querySelector('h3');
        if (!titleElement) continue;

        const linkElement = element.querySelector('a');
        const snippetElement = element.querySelector('div.VwiC3b');

        if (titleElement && linkElement) {
          const title = titleElement.textContent.trim();
          const url = linkElement.href;
          const snippet = snippetElement ? snippetElement.textContent.trim() : '';

          searchResults.push({ title, url, snippet });

          // Limit to 10 results
          if (searchResults.length >= 10) break;
        }
      }

      return searchResults;
    });

    // Close browser
    await browser.close();

    return {
      success: true,
      message: `Found ${results.length} search results for "${query}"`,
      results,
      query,
      domain: domain || null
    };
  } catch (error) {
    return {
      success: false,
      error: `Error searching the web: ${error.message}`
    };
  }
}

async function executeBrowserPreview({ url, name }) {
  try {
    if (!url) {
      return { success: false, error: 'URL is required' };
    }

    // Validate URL (check if it's a valid URL or a local file)
    let finalUrl = url;

    // If it's a local file path, convert to file:// URL
    if (fs.existsSync(url) && !url.startsWith('http')) {
      finalUrl = `file://${path.resolve(url)}`;
    } else if (!url.startsWith('http') && !url.startsWith('file://')) {
      // If it's not a URL or local file, check if it's a directory containing index.html
      const potentialIndexPath = path.join(url, 'index.html');
      if (fs.existsSync(potentialIndexPath)) {
        finalUrl = `file://${path.resolve(potentialIndexPath)}`;
      } else {
        // If not a valid URL format, assume it's a local path and check if it exists
        return { success: false, error: `Invalid URL or file not found: ${url}` };
      }
    }

    // For local file URLs, start a local server if it's an HTML file
    let server = null;
    let localUrl = finalUrl;

    if (finalUrl.startsWith('file://')) {
      const filePath = finalUrl.replace('file://', '');

      // Check if the path is a directory or HTML file
      if (fs.existsSync(filePath)) {
        // Get the directory containing the file
        const dirPath = fs.statSync(filePath).isDirectory() ? filePath : path.dirname(filePath);

        // Start a local server
        server = createServer({
          root: dirPath,
          cache: -1,
          cors: true,
          port: 0 // Use any available port
        });

        // Wait for the server to start
        await new Promise(resolve => {
          server.listen(0, () => {
            const port = server.server.address().port;

            // If filePath is a directory, use index.html, otherwise use the filename
            const fileName = fs.statSync(filePath).isDirectory() ? 'index.html' : path.basename(filePath);

            localUrl = `http://localhost:${port}/${fileName}`;
            resolve();
          });
        });
      }
    }

    // Open the URL in the default browser
    const openFn = await importOpen();
    await openFn(localUrl);

    return {
      success: true,
      message: `Opened ${name || 'preview'} in browser`,
      url: localUrl,
      originalUrl: url,
      name: name || 'Browser Preview',
      isLocalServer: !!server
    };
  } catch (error) {
    return {
      success: false,
      error: `Error opening browser preview: ${error.message}`
    };
  }
}

async function executeDeployWebApp({ framework, projectPath, subdomain, projectId }) {
  try {
    if (!projectPath) {
      return { success: false, error: 'Project path is required' };
    }

    if (!fs.existsSync(projectPath)) {
      return { success: false, error: `Project path does not exist: ${projectPath}` };
    }

    // Generate project ID if not provided
    const deploymentId = projectId || `proj-${uuidv4().substring(0, 8)}`;

    // Generate subdomain if not provided
    const deploymentSubdomain = subdomain || `${path.basename(projectPath)}-${uuidv4().substring(0, 6)}`;

    // Create a deployment record
    deployments[deploymentId] = {
      id: deploymentId,
      framework: framework || 'generic',
      projectPath,
      subdomain: deploymentSubdomain,
      status: 'preparing',
      createdAt: new Date().toISOString(),
      logs: []
    };

    // Log deployment info
    deployments[deploymentId].logs.push(`Preparing deployment for ${framework || 'generic'} project at ${projectPath}`);

    // Save deployment config
    fs.writeFileSync(config.deploymentConfigPath, JSON.stringify(deployments, null, 2), 'utf8');

    // Simulate deployment process (in a real implementation, this would call an actual deployment service)
    setTimeout(() => {
      deployments[deploymentId].status = 'deploying';
      deployments[deploymentId].logs.push(`Deploying project...`);

      // Update deployment config
      fs.writeFileSync(config.deploymentConfigPath, JSON.stringify(deployments, null, 2), 'utf8');

      // Simulate deployment completion after some time
      setTimeout(() => {
        deployments[deploymentId].status = 'deployed';
        deployments[deploymentId].deployedAt = new Date().toISOString();
        deployments[deploymentId].url = `https://${deploymentSubdomain}.example.com`;
        deployments[deploymentId].logs.push(`Deployment complete. Site available at ${deployments[deploymentId].url}`);

        // Update deployment config
        fs.writeFileSync(config.deploymentConfigPath, JSON.stringify(deployments, null, 2), 'utf8');
      }, 5000);
    }, 2000);

    return {
      success: true,
      message: `Deployment started for ${framework || 'generic'} project`,
      deploymentId,
      subdomain: deploymentSubdomain,
      status: 'preparing'
    };
  } catch (error) {
    return {
      success: false,
      error: `Error deploying web app: ${error.message}`
    };
  }
}

async function executeCheckDeployStatus({ deploymentId }) {
  try {
    if (!deploymentId) {
      return { success: false, error: 'Deployment ID is required' };
    }

    // Check if deployment exists
    if (!deployments[deploymentId]) {
      return { success: false, error: `Deployment with ID ${deploymentId} not found` };
    }

    const deployment = deployments[deploymentId];

    return {
      success: true,
      message: `Deployment status: ${deployment.status}`,
      deployment: {
        id: deployment.id,
        framework: deployment.framework,
        projectPath: deployment.projectPath,
        subdomain: deployment.subdomain,
        status: deployment.status,
        createdAt: deployment.createdAt,
        deployedAt: deployment.deployedAt,
        url: deployment.url,
        logs: deployment.logs
      }
    };
  } catch (error) {
    return {
      success: false,
      error: `Error checking deployment status: ${error.message}`
    };
  }
}

async function executeCreateMemory({ id, title, content, tags = [], corpusNames = [], action = 'create', userTriggered = true }) {
  try {
    if (!id) {
      return { success: false, error: 'Memory ID is required' };
    }

    if (action === 'create' || action === 'update') {
      if (!title) {
        return { success: false, error: 'Memory title is required' };
      }

      if (!content) {
        return { success: false, error: 'Memory content is required' };
      }

      // Create or update memory
      memories[id] = {
        id,
        title,
        content,
        tags: Array.isArray(tags) ? tags : tags ? [tags] : [],
        corpusNames: Array.isArray(corpusNames) ? corpusNames : corpusNames ? [corpusNames] : [],
        createdAt: memories[id]?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userTriggered: !!userTriggered
      };

      // Save memory to file
      const memoryFilePath = path.join(config.memoriesPath, `${id}.json`);
      fs.writeFileSync(memoryFilePath, JSON.stringify(memories[id], null, 2), 'utf8');

      return {
        success: true,
        message: action === 'create' ? `Memory "${title}" created` : `Memory "${title}" updated`,
        memory: memories[id]
      };
    } else if (action === 'delete') {
      // Check if memory exists
      if (!memories[id]) {
        return { success: false, error: `Memory with ID ${id} not found` };
      }

      // Get memory title before deletion
      const title = memories[id].title;

      // Delete memory
      delete memories[id];

      // Delete memory file
      const memoryFilePath = path.join(config.memoriesPath, `${id}.json`);
      if (fs.existsSync(memoryFilePath)) {
        fs.unlinkSync(memoryFilePath);
      }

      return {
        success: true,
        message: `Memory "${title}" deleted`,
        id
      };
    } else {
      return { success: false, error: `Invalid action: ${action}. Must be 'create', 'update', or 'delete'` };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error managing memory: ${error.message}`
    };
  }
}

async function executeFindByName({ searchDirectory, pattern, excludes = [], type = 'all', maxDepth = -1, extensions = [], fullPath = true }) {
  try {
    if (!searchDirectory) {
      return { success: false, error: 'Search directory is required' };
    }

    if (!pattern) {
      return { success: false, error: 'Search pattern is required' };
    }

    // Check if search directory exists
    if (!fs.existsSync(searchDirectory)) {
      return { success: false, error: `Search directory does not exist: ${searchDirectory}` };
    }

    // Convert pattern to regex
    const patternRegex = new RegExp(pattern.replace(/\*/g, '.*'), 'i');

    // Convert excludes to regex
    const excludeRegexes = excludes.map(exclude =>
      new RegExp(exclude.replace(/\*/g, '.*'), 'i')
    );

    // Convert extensions to lowercase array
    const extensionsList = Array.isArray(extensions)
      ? extensions.map(ext => ext.toLowerCase().replace(/^\./, ''))
      : [];

    // Recursive function to find files
    const findItems = (directory, currentDepth = 0) => {
      // Check max depth
      if (maxDepth > 0 && currentDepth > maxDepth) {
        return [];
      }

      const items = [];
      const entries = fs.readdirSync(directory, { withFileTypes: true });

      for (const entry of entries) {
        const entryPath = path.join(directory, entry.name);

        // Check if path is excluded
        if (excludeRegexes.some(regex => regex.test(entryPath))) {
          continue;
        }

        // Check if name matches pattern
        const nameMatches = patternRegex.test(entry.name);

        // Check file type
        const isDir = entry.isDirectory();
        const typeMatches =
          type === 'all' ||
          (type === 'file' && !isDir) ||
          (type === 'directory' && isDir);

        // Check extension if specified
        let extensionMatches = true;
        if (!isDir && extensionsList.length > 0) {
          const fileExtension = path.extname(entry.name).replace(/^\./, '').toLowerCase();
          extensionMatches = extensionsList.includes(fileExtension);
        }

        // Add item if it matches criteria
        if (nameMatches && typeMatches && extensionMatches) {
          items.push({
            name: entry.name,
            path: fullPath ? entryPath : entry.name,
            type: isDir ? 'directory' : 'file',
            extension: isDir ? null : path.extname(entry.name).replace(/^\./, '')
          });
        }

        // Recursively search subdirectories
        if (isDir) {
          const subItems = findItems(entryPath, currentDepth + 1);
          items.push(...subItems);
        }
      }

      return items;
    };

    // Start search
    const results = findItems(searchDirectory);

    return {
      success: true,
      message: `Found ${results.length} items matching "${pattern}"`,
      results,
      pattern,
      searchDirectory
    };
  } catch (error) {
    return {
      success: false,
      error: `Error finding items: ${error.message}`
    };
  }
}

async function executeViewFile({ absolutePath, startLine = 1, endLine, includeSummaryOfOtherLines = true }) {
  try {
    if (!absolutePath) {
      return { success: false, error: 'File path is required' };
    }

    // Check if file exists
    if (!fs.existsSync(absolutePath)) {
      return { success: false, error: `File does not exist: ${absolutePath}` };
    }

    // Check if it's a file, not a directory
    const stats = fs.statSync(absolutePath);
    if (!stats.isFile()) {
      return { success: false, error: `Not a file: ${absolutePath}` };
    }

    // Read file content
    const content = fs.readFileSync(absolutePath, 'utf8');
    const lines = content.split('\n');

    // Adjust line numbers
    startLine = Math.max(1, startLine);
    endLine = endLine ? Math.min(lines.length, endLine) : lines.length;

    // Get requested lines
    const requestedLines = lines.slice(startLine - 1, endLine);

    // Prepare result
    const result = {
      success: true,
      message: `Showing lines ${startLine} to ${endLine} of ${lines.length}`,
      filename: path.basename(absolutePath),
      path: absolutePath,
      totalLines: lines.length,
      startLine,
      endLine,
      content: requestedLines.join('\n')
    };

    // Include summary if requested
    if (includeSummaryOfOtherLines) {
      if (startLine > 1) {
        result.beforeSummary = `[Lines 1-${startLine - 1} omitted]`;
      }

      if (endLine < lines.length) {
        result.afterSummary = `[Lines ${endLine + 1}-${lines.length} omitted]`;
      }
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: `Error viewing file: ${error.message}`
    };
  }
}

async function executeWriteToFile({ targetFile, codeContent, emptyFile = false }) {
  try {
    if (!targetFile) {
      return { success: false, error: 'Target file path is required' };
    }

    // Create directories if they don't exist
    const dirPath = path.dirname(targetFile);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }

    // Write content to file
    if (emptyFile) {
      // Create an empty file
      fs.writeFileSync(targetFile, '', 'utf8');
    } else if (codeContent) {
      // Write the provided content
      fs.writeFileSync(targetFile, codeContent, 'utf8');
    } else {
      return { success: false, error: 'File content is required' };
    }

    return {
      success: true,
      message: `File ${targetFile} written successfully`,
      targetFile,
      size: emptyFile ? 0 : codeContent.length
    };
  } catch (error) {
    return {
      success: false,
      error: `Error writing to file: ${error.message}`
    };
  }
}

async function executeCodebaseSearch({ query, targetDirectories = ['.'] }) {
  try {
    if (!query) {
      return { success: false, error: 'Search query is required' };
    }

    // Validate target directories
    const validDirectories = targetDirectories.filter(dir => fs.existsSync(dir));

    if (validDirectories.length === 0) {
      return { success: false, error: 'No valid target directories found' };
    }

    // Build search command
    const searchDirs = validDirectories.join(' ');
    const command = `grep -r -l "${query}" ${searchDirs}`;

    try {
      // Find matching files
      const { stdout } = await execAsync(command);
      const matchingFiles = stdout.trim().split('\n').filter(Boolean);

      // Get context for each match
      const results = [];

      for (const file of matchingFiles) {
        // Get line numbers of matches
        const { stdout: matchLines } = await execAsync(`grep -n "${query}" "${file}"`);

        const fileMatches = matchLines.trim().split('\n').map(line => {
          // Parse line number and content
          const match = line.match(/^(\d+):(.*)/);
          if (match) {
            const lineNumber = parseInt(match[1]);
            const lineContent = match[2];

            // Read surrounding lines for context
            const fileContent = fs.readFileSync(file, 'utf8').split('\n');
            const contextStart = Math.max(0, lineNumber - 3);
            const contextEnd = Math.min(fileContent.length - 1, lineNumber + 2);
            const context = fileContent.slice(contextStart, contextEnd + 1);

            return {
              file,
              lineNumber,
              lineContent,
              context,
              contextStart: contextStart + 1, // 1-indexed
              contextEnd: contextEnd + 1      // 1-indexed
            };
          }
          return null;
        }).filter(Boolean);

        results.push(...fileMatches);
      }

      return {
        success: true,
        message: `Found ${results.length} matches in ${matchingFiles.length} files`,
        results,
        query,
        targetDirectories: validDirectories
      };
    } catch (error) {
      // grep returns exit code 1 if no matches found
      if (error.code === 1) {
        return {
          success: true,
          message: `No matches found for "${query}"`,
          results: [],
          query,
          targetDirectories: validDirectories
        };
      }

      throw error;
    }
  } catch (error) {
    return {
      success: false,
      error: `Error searching codebase: ${error.message}`
    };
  }
}

async function executeReadDeploymentConfig({ projectPath }) {
  try {
    if (!projectPath) {
      return { success: false, error: 'Project path is required' };
    }

    // Check if project path exists
    if (!fs.existsSync(projectPath)) {
      return { success: false, error: `Project path does not exist: ${projectPath}` };
    }

    // Common deployment config files
    const configFiles = [
      'vercel.json',
      'netlify.toml',
      'firebase.json',
      '.github/workflows/deploy.yml',
      '.gitlab-ci.yml',
      'docker-compose.yml',
      'Dockerfile',
      '.travis.yml',
      'now.json',
      'serverless.yml',
      'appspec.yml',
      'app.yaml',
      'k8s',
      'kubernetes',
      '.circleci/config.yml'
    ];

    // Look for config files
    const foundConfigs = [];

    for (const configFile of configFiles) {
      const fullPath = path.join(projectPath, configFile);

      if (fs.existsSync(fullPath)) {
        try {
          const content = fs.readFileSync(fullPath, 'utf8');

          foundConfigs.push({
            path: fullPath,
            name: path.basename(configFile),
            content
          });
        } catch (e) {
          // Skip files that can't be read
        }
      }
    }

    // Also check for deployment scripts in package.json
    const packageJsonPath = path.join(projectPath, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

        if (packageJson.scripts) {
          const deployScripts = Object.entries(packageJson.scripts)
            .filter(([name, script]) =>
              name.includes('deploy') ||
              script.includes('deploy') ||
              script.includes('vercel') ||
              script.includes('netlify') ||
              script.includes('firebase') ||
              script.includes('heroku')
            );

          if (deployScripts.length > 0) {
            foundConfigs.push({
              path: packageJsonPath,
              name: 'package.json',
              deployScripts: Object.fromEntries(deployScripts)
            });
          }
        }
      } catch (e) {
        // Skip if package.json can't be parsed
      }
    }

    if (foundConfigs.length === 0) {
      return {
        success: true,
        message: 'No deployment configuration found',
        projectPath,
        configs: []
      };
    }

    return {
      success: true,
      message: `Found ${foundConfigs.length} deployment configurations`,
      projectPath,
      configs: foundConfigs
    };
  } catch (error) {
    return {
      success: false,
      error: `Error reading deployment config: ${error.message}`
    };
  }
}

async function executeViewCodeItem({ nodePath }) {
  try {
    if (!nodePath) {
      return { success: false, error: 'Node path is required' };
    }

    // Extract file path and item path from node path
    const parts = nodePath.split('#');
    const filePath = parts[0];
    const itemPath = parts.length > 1 ? parts[1] : null;

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return { success: false, error: `File does not exist: ${filePath}` };
    }

    // Read file content
    const content = fs.readFileSync(filePath, 'utf8');

    // If no specific item is requested, return the whole file
    if (!itemPath) {
      return {
        success: true,
        message: `Showing entire file: ${filePath}`,
        filePath,
        content
      };
    }

    // Parse the file based on extension
    const extension = path.extname(filePath).toLowerCase();

    if (['.js', '.ts', '.jsx', '.tsx'].includes(extension)) {
      // For JavaScript/TypeScript files, try to find the requested item
      // This is a simplified implementation - a real one would use an AST parser

      // Function or class definition (simplified)
      const itemRegex = new RegExp(`(function|class|const|let|var)\\s+${itemPath}\\s*[({=]`);
      const match = content.match(itemRegex);

      if (match) {
        const startIndex = match.index;

        // Find the end of the item (simplified)
        let depth = 0;
        let endIndex = startIndex;

        for (let i = startIndex; i < content.length; i++) {
          const char = content[i];

          if (char === '{' || char === '(') {
            depth++;
          } else if (char === '}' || char === ')') {
            depth--;

            if (depth <= 0) {
              endIndex = i + 1;
              break;
            }
          }
        }

        const itemContent = content.substring(startIndex, endIndex);

        return {
          success: true,
          message: `Found item ${itemPath} in ${filePath}`,
          filePath,
          itemPath,
          content: itemContent
        };
      }

      return {
        success: false,
        error: `Item ${itemPath} not found in ${filePath}`
      };
    }

    // For other file types, return the whole file
    return {
      success: true,
      message: `Showing entire file (item-specific view not supported for this file type): ${filePath}`,
      filePath,
      content
    };
  } catch (error) {
    return {
      success: false,
      error: `Error viewing code item: ${error.message}`
    };
  }
}

async function executeViewWebDocumentContentChunk({ url, position = 0 }) {
  try {
    if (!url) {
      return { success: false, error: 'URL is required' };
    }

    // Validate URL
    try {
      new URL(url);
    } catch (e) {
      return { success: false, error: `Invalid URL: ${url}` };
    }

    // Fetch content
    const response = await axios.get(url);

    // Check if content is HTML
    const contentType = response.headers['content-type'] || '';
    const isHtml = contentType.includes('text/html');

    if (isHtml) {
      // Parse HTML content
      const dom = new JSDOM(response.data);
      const document = dom.window.document;

      // Extract text content
      let textContent = '';

      // Try to find main content area
      const mainElement = document.querySelector('main') ||
                          document.querySelector('article') ||
                          document.querySelector('.content') ||
                          document.querySelector('#content') ||
                          document.body;

      if (mainElement) {
        // Remove scripts, styles, and other non-content elements
        const scriptsAndStyles = mainElement.querySelectorAll('script, style, nav, footer, header, aside, form');
        for (const element of scriptsAndStyles) {
          if (element.parentNode) {
            element.parentNode.removeChild(element);
          }
        }

        textContent = mainElement.textContent
          .replace(/\s+/g, ' ')
          .trim();
      }

      // Split content into chunks
      const chunkSize = 4000; // Characters per chunk
      const chunks = [];

      for (let i = 0; i < textContent.length; i += chunkSize) {
        chunks.push(textContent.substring(i, i + chunkSize));
      }

      // Validate position
      const validPosition = Math.min(Math.max(0, position), chunks.length - 1);

      return {
        success: true,
        message: `Showing chunk ${validPosition + 1} of ${chunks.length} from ${url}`,
        url,
        title: document.title,
        totalChunks: chunks.length,
        position: validPosition,
        content: chunks[validPosition],
        hasNextChunk: validPosition < chunks.length - 1,
        hasPreviousChunk: validPosition > 0
      };
    } else {
      // For non-HTML content, return as is
      return {
        success: true,
        message: `Content from ${url} (not HTML)`,
        url,
        content: typeof response.data === 'string' ? response.data : JSON.stringify(response.data, null, 2),
        contentType
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error viewing web document: ${error.message}`
    };
  }
}

async function executeSuggestedResponses({ suggestions }) {
  try {
    if (!suggestions || !Array.isArray(suggestions) || suggestions.length === 0) {
      return { success: false, error: 'Suggestions array is required and must not be empty' };
    }

    // Filter out invalid suggestions
    const validSuggestions = suggestions.filter(suggestion =>
      suggestion && typeof suggestion === 'string' && suggestion.trim() !== ''
    );

    if (validSuggestions.length === 0) {
      return { success: false, error: 'No valid suggestions provided' };
    }

    return {
      success: true,
      message: `Providing ${validSuggestions.length} suggested responses`,
      suggestions: validSuggestions
    };
  } catch (error) {
    return {
      success: false,
      error: `Error providing suggested responses: ${error.message}`
    };
  }
}

async function executeAnalyzeCode({ codePath, language, analysisType = 'general' }) {
  try {
    if (!codePath) {
      return { success: false, error: 'Code path is required' };
    }

    // Check if file exists
    if (!fs.existsSync(codePath)) {
      return { success: false, error: `File does not exist: ${codePath}` };
    }

    // Read file content
    const code = fs.readFileSync(codePath, 'utf8');

    // Determine language from file extension if not provided
    let codeLanguage = language;
    if (!codeLanguage) {
      const extension = path.extname(codePath).toLowerCase();
      const languageMap = {
        '.js': 'JavaScript',
        '.ts': 'TypeScript',
        '.py': 'Python',
        '.java': 'Java',
        '.c': 'C',
        '.cpp': 'C++',
        '.cs': 'C#',
        '.go': 'Go',
        '.rb': 'Ruby',
        '.php': 'PHP',
        '.swift': 'Swift',
        '.kt': 'Kotlin',
        '.rs': 'Rust',
        '.html': 'HTML',
        '.css': 'CSS',
        '.jsx': 'React JSX',
        '.tsx': 'React TSX',
        '.json': 'JSON',
        '.md': 'Markdown',
        '.yml': 'YAML',
        '.xml': 'XML',
        '.sql': 'SQL'
      };

      codeLanguage = languageMap[extension] || 'Unknown';
    }

    // Generate appropriate prompt based on analysis type
    let analysisPrompt;

    switch (analysisType) {
      case 'security':
        analysisPrompt = `
Analyze the following ${codeLanguage} code for security vulnerabilities:

\`\`\`${codeLanguage}
${code}
\`\`\`

Identify any security issues such as:
1. Injection vulnerabilities
2. Authentication problems
3. Sensitive data exposure
4. Security misconfigurations
5. Cross-site scripting (XSS)
6. Insecure dependencies
7. Insecure use of cryptography
8. Any other security concerns

For each issue:
- Describe the vulnerability
- Rate its severity (Critical, High, Medium, Low)
- Explain how it could be exploited
- Suggest a fix

Format your response as JSON with this structure:
{
  "vulnerabilities": [
    {
      "type": "vulnerability type",
      "severity": "severity level",
      "description": "detailed description",
      "location": "where in the code",
      "recommendation": "how to fix it"
    }
  ],
  "summary": "brief summary of findings",
  "overallRisk": "overall risk assessment"
}
`;
        break;

      case 'performance':
        analysisPrompt = `
Analyze the following ${codeLanguage} code for performance issues:

\`\`\`${codeLanguage}
${code}
\`\`\`

Identify any performance concerns such as:
1. Inefficient algorithms (O(n²) or worse)
2. Unnecessary computations
3. Memory leaks
4. Excessive resource usage
5. Blocking operations
6. Redundant operations
7. Any other performance bottlenecks

For each issue:
- Describe the performance problem
- Rate its impact (Critical, High, Medium, Low)
- Explain why it's inefficient
- Suggest an optimization

Format your response as JSON with this structure:
{
  "performanceIssues": [
    {
      "type": "issue type",
      "impact": "impact level",
      "description": "detailed description",
      "location": "where in the code",
      "recommendation": "how to optimize it"
    }
  ],
  "summary": "brief summary of findings",
  "overallPerformance": "overall performance assessment"
}
`;
        break;

      case 'style':
        analysisPrompt = `
Analyze the following ${codeLanguage} code for style and readability:

\`\`\`${codeLanguage}
${code}
\`\`\`

Identify any style or readability issues such as:
1. Inconsistent formatting
2. Poor naming conventions
3. Lack of comments or documentation
4. Overly complex code
5. Duplication
6. Violation of common coding standards
7. Any other readability concerns

For each issue:
- Describe the style problem
- Suggest an improvement

Format your response as JSON with this structure:
{
  "styleIssues": [
    {
      "type": "issue type",
      "description": "detailed description",
      "location": "where in the code",
      "recommendation": "how to improve it"
    }
  ],
  "summary": "brief summary of findings",
  "overallReadability": "overall readability assessment"
}
`;
        break;

      default: // general analysis
        analysisPrompt = `
Analyze the following ${codeLanguage} code comprehensively:

\`\`\`${codeLanguage}
${code}
\`\`\`

Provide a detailed analysis including:
1. Code structure and organization
2. Potential bugs or errors
3. Performance considerations
4. Security concerns
5. Style and readability
6. Best practices conformance
7. Suggestions for improvement

Format your response as JSON with this structure:
{
  "summary": "brief summary of the code",
  "issues": [
    {
      "type": "issue type (bug, performance, security, style, etc.)",
      "severity": "severity level if applicable",
      "description": "detailed description",
      "location": "where in the code",
      "recommendation": "how to address it"
    }
  ],
  "strengths": ["list of code strengths"],
  "suggestions": ["list of general improvement suggestions"],
  "overallAssessment": "overall quality assessment"
}
`;
    }

    // Call AI to analyze the code
    const analysisResult = await model.generateContent(analysisPrompt);
    const analysisText = analysisResult.response.text();

    // Extract JSON response
    let analysis;
    try {
      // Try to parse the entire response as JSON
      analysis = JSON.parse(analysisText);
    } catch (e) {
      // If that fails, try to extract JSON from text
      const jsonMatch = analysisText.match(/```json\n([\s\S]*?)\n```/) ||
                        analysisText.match(/({[\s\S]*})/);

      if (jsonMatch) {
        analysis = JSON.parse(jsonMatch[1].trim());
      } else {
        // If JSON parsing fails, return the raw analysis
        return {
          success: true,
          message: `Code analysis completed for ${path.basename(codePath)}`,
          codePath,
          language: codeLanguage,
          analysisType,
          rawAnalysis: analysisText
        };
      }
    }

    return {
      success: true,
      message: `Code analysis completed for ${path.basename(codePath)}`,
      codePath,
      language: codeLanguage,
      analysisType,
      analysis
    };
  } catch (error) {
    return {
      success: false,
      error: `Error analyzing code: ${error.message}`
    };
  }
}

async function executeGenerateProject({ projectName, framework, language, features = [] }) {
  try {
    if (!projectName) {
      return { success: false, error: 'Project name is required' };
    }

    // Sanitize project name for directory creation
    const sanitizedProjectName = projectName.replace(/[^a-zA-Z0-9-_]/g, '-').toLowerCase();
    const projectPath = path.resolve(sanitizedProjectName);

    // Check if project directory already exists
    if (fs.existsSync(projectPath)) {
      return { success: false, error: `Project directory already exists: ${projectPath}` };
    }

    // Create project directory
    fs.mkdirSync(projectPath, { recursive: true });

    // Determine language if not specified
    const projectLanguage = language || (framework ? getDefaultLanguage(framework) : 'JavaScript');

    // Generate project structure based on framework
    let projectStructure;
    let setupCommands = [];

    if (framework) {
      // Use framework-specific generator if available
      const result = await generateFrameworkProject(projectPath, framework, projectLanguage, features);

      if (!result.success) {
        // Clean up partial project
        if (fs.existsSync(projectPath)) {
          fs.rmSync(projectPath, { recursive: true, force: true });
        }

        return result;
      }

      projectStructure = result.structure;
      setupCommands = result.commands;
    } else {
      // Generate a simple project structure
      const result = await generateSimpleProject(projectPath, projectLanguage, features);

      if (!result.success) {
        // Clean up partial project
        if (fs.existsSync(projectPath)) {
          fs.rmSync(projectPath, { recursive: true, force: true });
        }

        return result;
      }

      projectStructure = result.structure;
      setupCommands = result.commands;
    }

    // Create a README file
    const readmePath = path.join(projectPath, 'README.md');
    const readmeContent = generateReadme(projectName, framework, projectLanguage, features);
    fs.writeFileSync(readmePath, readmeContent, 'utf8');

    return {
      success: true,
      message: `Project ${projectName} created successfully`,
      projectName,
      framework: framework || 'none',
      language: projectLanguage,
      features,
      projectPath,
      structure: projectStructure,
      setupCommands
    };
  } catch (error) {
    return {
      success: false,
      error: `Error generating project: ${error.message}`
    };
  }
}

// Helper function to determine default language for a framework
function getDefaultLanguage(framework) {
  const frameworkLanguageMap = {
    'react': 'JavaScript',
    'vue': 'JavaScript',
    'angular': 'TypeScript',
    'next': 'JavaScript',
    'nuxt': 'JavaScript',
    'svelte': 'JavaScript',
    'express': 'JavaScript',
    'fastapi': 'Python',
    'django': 'Python',
    'flask': 'Python',
    'spring': 'Java',
    'rails': 'Ruby',
    'laravel': 'PHP',
    'dotnet': 'C#'
  };

  return frameworkLanguageMap[framework.toLowerCase()] || 'JavaScript';
}

// Helper function to generate a simple project
async function generateSimpleProject(projectPath, language, features) {
  try {
    let structure = [];
    let commands = [];

    switch (language.toLowerCase()) {
      case 'javascript':
        // Create package.json
        const packageJson = {
          name: path.basename(projectPath),
          version: '1.0.0',
          description: 'A JavaScript project',
          main: 'index.js',
          scripts: {
            start: 'node index.js',
            test: 'echo "Error: no test specified" && exit 1'
          },
          keywords: [],
          author: '',
          license: 'ISC'
        };

        fs.writeFileSync(
          path.join(projectPath, 'package.json'),
          JSON.stringify(packageJson, null, 2),
          'utf8'
        );

        // Create main JS file
        fs.writeFileSync(
          path.join(projectPath, 'index.js'),
          'console.log("Hello, world!");',
          'utf8'
        );

        // Create a .gitignore file
        fs.writeFileSync(
          path.join(projectPath, '.gitignore'),
          'node_modules\n.env\n.DS_Store',
          'utf8'
        );

        structure = ['package.json', 'index.js', '.gitignore'];
        commands = ['npm install'];
        break;

      case 'python':
        // Create main.py
        fs.writeFileSync(
          path.join(projectPath, 'main.py'),
          'print("Hello, world!")',
          'utf8'
        );

        // Create requirements.txt
        fs.writeFileSync(
          path.join(projectPath, 'requirements.txt'),
          '# Add your dependencies here',
          'utf8'
        );

        // Create .gitignore
        fs.writeFileSync(
          path.join(projectPath, '.gitignore'),
          '__pycache__/\n*.py[cod]\n*$py.class\n.env\n.venv\nenv/\nvenv/\nENV/\nenv.bak/\nvenv.bak/\n.DS_Store',
          'utf8'
        );

        structure = ['main.py', 'requirements.txt', '.gitignore'];
        commands = ['python -m venv venv', 'source venv/bin/activate'];
        break;

      // Add more languages as needed

      default:
        return {
          success: false,
          error: `Unsupported language: ${language}`
        };
    }

    return {
      success: true,
      structure,
      commands
    };
  } catch (error) {
    return {
      success: false,
      error: `Error generating simple project: ${error.message}`
    };
  }
}

// Helper function to generate a framework-specific project
async function generateFrameworkProject(projectPath, framework, language, features) {
  try {
    // For real implementation, this could call framework-specific CLI tools
    // For now, just return instructions on how to create the project

    let commands = [];
    let structure = [];

    switch (framework.toLowerCase()) {
      case 'react':
        commands = [
          `npx create-react-app ${path.basename(projectPath)}`,
          `cd ${path.basename(projectPath)}`,
          'npm start'
        ];

        structure = [
          'public/',
          'src/',
          'package.json',
          'README.md',
          '.gitignore'
        ];
        break;

      case 'vue':
        commands = [
          `npx @vue/cli create ${path.basename(projectPath)}`,
          `cd ${path.basename(projectPath)}`,
          'npm run serve'
        ];

        structure = [
          'public/',
          'src/',
          'package.json',
          'README.md',
          '.gitignore'
        ];
        break;

      // Add more frameworks as needed

      default:
        return {
          success: false,
          error: `Unsupported framework: ${framework}`
        };
    }

    return {
      success: true,
      structure,
      commands,
      note: "This is a placeholder. In a real implementation, the project files would be created directly."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error generating framework project: ${error.message}`
    };
  }
}

// Helper function to generate a README file
function generateReadme(projectName, framework, language, features) {
  const featuresList = features.length > 0
    ? `\n\n## Features\n\n${features.map(feature => `- ${feature}`).join('\n')}`
    : '';

  const frameworkInfo = framework
    ? `\nThis project was built with ${framework}.`
    : '';

  return `# ${projectName}

A ${language} project.${frameworkInfo}${featuresList}

## Getting Started

1. Clone this repository
2. Install dependencies
3. Run the project

## License

This project is licensed under the ISC License.
`;
}

// Implement executeInstallDependencies function
async function executeInstallDependencies({ packageManager, dependencies = [], projectPath = '.', dev = false }) {
  try {
    if (!fs.existsSync(projectPath)) {
      return { success: false, error: `Project path does not exist: ${projectPath}` };
    }

    // Determine package manager if not specified
    let pm = packageManager;
    if (!pm) {
      // Auto-detect package manager based on project files
      if (fs.existsSync(path.join(projectPath, 'package.json'))) {
        if (fs.existsSync(path.join(projectPath, 'yarn.lock'))) {
          pm = 'yarn';
        } else {
          pm = 'npm';
        }
      } else if (fs.existsSync(path.join(projectPath, 'requirements.txt'))) {
        pm = 'pip';
      } else if (fs.existsSync(path.join(projectPath, 'Gemfile'))) {
        pm = 'bundle';
      } else if (fs.existsSync(path.join(projectPath, 'pom.xml'))) {
        pm = 'mvn';
      } else if (fs.existsSync(path.join(projectPath, 'build.gradle'))) {
        pm = 'gradle';
      } else {
        return { success: false, error: 'Could not determine package manager. Please specify.' };
      }
    }

    // Construct command based on package manager
    let command;
    switch (pm.toLowerCase()) {
      case 'npm':
        command = `npm install ${dev ? '--save-dev ' : ''}${dependencies.join(' ')}`;
        break;
      case 'yarn':
        command = `yarn add ${dev ? '--dev ' : ''}${dependencies.join(' ')}`;
        break;
      case 'pip':
        command = `pip install ${dependencies.join(' ')}`;
        break;
      case 'bundle':
        // For bundle, we don't directly install packages, just note that Gemfile should be updated
        return {
          success: true,
          message: 'For Ruby projects, add the gems to your Gemfile then run "bundle install"',
          dependencies,
          packageManager: pm,
          nextStep: 'Run "bundle install" after updating Gemfile'
        };
      case 'mvn':
        return {
          success: true,
          message: 'For Maven projects, add dependencies to pom.xml then run "mvn install"',
          dependencies,
          packageManager: pm,
          nextStep: 'Run "mvn install" after updating pom.xml'
        };
      case 'gradle':
        return {
          success: true,
          message: 'For Gradle projects, add dependencies to build.gradle then run "gradle build"',
          dependencies,
          packageManager: pm,
          nextStep: 'Run "gradle build" after updating build.gradle'
        };
      default:
        return { success: false, error: `Unsupported package manager: ${pm}` };
    }

    // Execute the command
    const result = await executeRunCommand({
      commandLine: command,
      cwd: projectPath,
      blocking: true
    });

    if (result.success) {
      return {
        success: true,
        message: `Successfully installed dependencies using ${pm}`,
        dependencies,
        packageManager: pm,
        projectPath,
        commandOutput: result.stdout
      };
    } else {
      return {
        success: false,
        error: `Failed to install dependencies: ${result.error}`,
        stderr: result.stderr
      };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error installing dependencies: ${error.message}`
    };
  }
}

// Implement executeDatabaseOperation function
async function executeDatabaseOperation({ operation, dbType, connectionString, query }) {
  try {
    if (!operation) {
      return { success: false, error: 'Database operation is required' };
    }

    if (!dbType) {
      return { success: false, error: 'Database type is required' };
    }

    // This is a simplified implementation - in a real scenario, you'd use
    // actual database connectors based on dbType

    return {
      success: true,
      message: `Database operation simulation for ${operation} on ${dbType}`,
      operation,
      dbType,
      note: "This is a placeholder. In a real implementation, actual database operations would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error executing database operation: ${error.message}`
    };
  }
}

// Implement executeApiIntegration function
async function executeApiIntegration({ apiName, endpoint, method = 'GET', headers = {}, body = null }) {
  try {
    if (!apiName) {
      return { success: false, error: 'API name is required' };
    }

    if (!endpoint) {
      return { success: false, error: 'API endpoint is required' };
    }

    // Validate method
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
    if (!validMethods.includes(method.toUpperCase())) {
      return { success: false, error: `Invalid HTTP method: ${method}. Must be one of ${validMethods.join(', ')}` };
    }

    // Simulated API call - in a real implementation, would use axios or similar
    return {
      success: true,
      message: `API integration simulation for ${apiName} using ${method} to ${endpoint}`,
      apiName,
      endpoint,
      method,
      note: "This is a placeholder. In a real implementation, actual API calls would be made."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error integrating with API: ${error.message}`
    };
  }
}

// Implement executeDeployBackend function
async function executeDeployBackend({ servicePath, platform, region, environment }) {
  try {
    if (!servicePath) {
      return { success: false, error: 'Service path is required' };
    }

    if (!fs.existsSync(servicePath)) {
      return { success: false, error: `Service path does not exist: ${servicePath}` };
    }

    if (!platform) {
      return { success: false, error: 'Deployment platform is required' };
    }

    // Deploy based on platform (simulation)
    const deploymentId = `deploy-${uuidv4().substring(0, 8)}`;

    return {
      success: true,
      message: `Backend deployment simulation for ${platform}`,
      deploymentId,
      servicePath,
      platform,
      region: region || 'default',
      environment: environment || 'development',
      note: "This is a placeholder. In a real implementation, actual deployment would occur."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error deploying backend: ${error.message}`
    };
  }
}

// Implement executeSetupCiCd function
async function executeSetupCiCd({ provider, projectPath, steps = [] }) {
  try {
    if (!provider) {
      return { success: false, error: 'CI/CD provider is required' };
    }

    if (!projectPath) {
      return { success: false, error: 'Project path is required' };
    }

    if (!fs.existsSync(projectPath)) {
      return { success: false, error: `Project path does not exist: ${projectPath}` };
    }

    // Generate CI/CD configuration file based on provider
    let configFile;
    let configContent;

    switch (provider.toLowerCase()) {
      case 'github':
      case 'github actions':
        configFile = path.join(projectPath, '.github/workflows/ci-cd.yml');
        configContent = `name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Build
        run: echo "Building project..."
      - name: Test
        run: echo "Running tests..."
      - name: Deploy
        run: echo "Deploying project..."
`;
        break;

      case 'gitlab':
      case 'gitlab ci':
        configFile = path.join(projectPath, '.gitlab-ci.yml');
        configContent = `stages:
  - build
  - test
  - deploy

build:
  stage: build
  script:
    - echo "Building project..."

test:
  stage: test
  script:
    - echo "Running tests..."

deploy:
  stage: deploy
  script:
    - echo "Deploying project..."
  only:
    - main
`;
        break;

      case 'travis':
      case 'travis ci':
        configFile = path.join(projectPath, '.travis.yml');
        configContent = `language: node_js
node_js:
  - 14
script:
  - echo "Building project..."
  - echo "Running tests..."
deploy:
  provider: script
  script: echo "Deploying project..."
  on:
    branch: main
`;
        break;

      default:
        return { success: false, error: `Unsupported CI/CD provider: ${provider}` };
    }

    // Create directory if it doesn't exist
    const dir = path.dirname(configFile);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Write the config file
    fs.writeFileSync(configFile, configContent, 'utf8');

    return {
      success: true,
      message: `CI/CD configuration for ${provider} created at ${configFile}`,
      provider,
      configFile,
      configContent
    };
  } catch (error) {
    return {
      success: false,
      error: `Error setting up CI/CD: ${error.message}`
    };
  }
}

// Implement executeAnalyzePerformance function
async function executeAnalyzePerformance({ targetUrl, metrics = [], duration = 30 }) {
  try {
    if (!targetUrl) {
      return { success: false, error: 'Target URL is required' };
    }

    // Simple validation of URL
    try {
      new URL(targetUrl);
    } catch (e) {
      return { success: false, error: `Invalid URL: ${targetUrl}` };
    }

    // This would normally launch real performance analysis tools
    // Here we just simulate it

    return {
      success: true,
      message: `Performance analysis simulation for ${targetUrl}`,
      targetUrl,
      metrics: metrics.length > 0 ? metrics : ['loadTime', 'firstContentfulPaint', 'totalBlockingTime'],
      duration,
      results: {
        loadTime: '1.2s',
        firstContentfulPaint: '0.8s',
        totalBlockingTime: '120ms',
        performanceScore: 85
      },
      note: "This is a placeholder. In a real implementation, actual performance analysis would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error analyzing performance: ${error.message}`
    };
  }
}

// Implement executeGenerateDocumentation function
async function executeGenerateDocumentation({ sourcePath, outputFormat = 'markdown', outputPath }) {
  try {
    if (!sourcePath) {
      return { success: false, error: 'Source path is required' };
    }

    if (!fs.existsSync(sourcePath)) {
      return { success: false, error: `Source path does not exist: ${sourcePath}` };
    }

    // Determine output path if not provided
    const finalOutputPath = outputPath || path.join(sourcePath, 'docs');

    // Create output directory if it doesn't exist
    if (!fs.existsSync(finalOutputPath)) {
      fs.mkdirSync(finalOutputPath, { recursive: true });
    }

    // This would normally use documentation generation tools
    // Here we just create a simple template
    const readmeContent = `# Project Documentation

## Overview
Documentation for the codebase at ${sourcePath}.

## File Structure
\`\`\`
${sourcePath}/
├── ...
\`\`\`

## Components
_Documentation for components would be generated here._

## API Reference
_API documentation would be generated here._

## Getting Started
_Getting started guide would be generated here._
`;

    const outputFile = path.join(finalOutputPath, 'README.md');
    fs.writeFileSync(outputFile, readmeContent, 'utf8');

    return {
      success: true,
      message: `Documentation generated at ${outputFile}`,
      sourcePath,
      outputPath: finalOutputPath,
      outputFormat,
      generatedFiles: [outputFile],
      note: "This is a placeholder. In a real implementation, comprehensive documentation would be generated."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error generating documentation: ${error.message}`
    };
  }
}

// Implement executeSetupMonitoring function
async function executeSetupMonitoring({ serviceName, metrics = [], alertThresholds = {} }) {
  try {
    if (!serviceName) {
      return { success: false, error: 'Service name is required' };
    }

    // This would normally set up real monitoring tools
    // Here we just simulate it

    return {
      success: true,
      message: `Monitoring setup simulation for ${serviceName}`,
      serviceName,
      metrics: metrics.length > 0 ? metrics : ['cpu', 'memory', 'requests', 'errors'],
      alertThresholds: Object.keys(alertThresholds).length > 0 ? alertThresholds : {
        cpu: '80%',
        memory: '85%',
        errorRate: '5%'
      },
      note: "This is a placeholder. In a real implementation, actual monitoring would be configured."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error setting up monitoring: ${error.message}`
    };
  }
}

// Implement executeScrapeWebsite function
async function executeScrapeWebsite({ url, selectors = {}, pagination = null, outputFormat = 'json' }) {
  try {
    if (!url) {
      return { success: false, error: 'URL is required' };
    }

    // Validate URL
    try {
      new URL(url);
    } catch (e) {
      return { success: false, error: `Invalid URL: ${url}` };
    }

    // This would normally use Puppeteer to scrape the website
    // Here we just simulate results

    return {
      success: true,
      message: `Website scraping simulation for ${url}`,
      url,
      selectors,
      pagination,
      outputFormat,
      results: [
        { title: "Sample Item 1", description: "Description for item 1" },
        { title: "Sample Item 2", description: "Description for item 2" }
      ],
      note: "This is a placeholder. In a real implementation, actual website scraping would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error scraping website: ${error.message}`
    };
  }
}

// Implement executeNaturalLanguageProcessing function
async function executeNaturalLanguageProcessing({ text, operation }) {
  try {
    if (!text) {
      return { success: false, error: 'Text is required' };
    }

    if (!operation) {
      return { success: false, error: 'Operation is required' };
    }

    // This would normally use NLP libraries or APIs
    // Here we just simulate results based on the operation

    let result;

    switch (operation.toLowerCase()) {
      case 'sentiment':
        result = {
          sentiment: text.length % 3 === 0 ? 'positive' : text.length % 3 === 1 ? 'neutral' : 'negative',
          score: ((text.length % 10) / 10).toFixed(2)
        };
        break;

      case 'entities':
        result = {
          entities: [
            { entity: 'example', type: 'ORGANIZATION', mentions: 1 },
            { entity: 'sample', type: 'CONCEPT', mentions: 2 }
          ]
        };
        break;

      case 'summarization':
        result = {
          summary: text.length > 100 ? text.substring(0, 100) + '...' : text
        };
        break;

      default:
        return { success: false, error: `Unsupported NLP operation: ${operation}` };
    }

    return {
      success: true,
      message: `NLP ${operation} operation completed`,
      operation,
      result,
      note: "This is a placeholder. In a real implementation, actual NLP processing would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error performing NLP operation: ${error.message}`
    };
  }
}

// Implement executeImageProcessing function
async function executeImageProcessing({ imagePath, operation, outputPath }) {
  try {
    if (!imagePath) {
      return { success: false, error: 'Image path is required' };
    }

    if (!fs.existsSync(imagePath)) {
      return { success: false, error: `Image does not exist: ${imagePath}` };
    }

    if (!operation) {
      return { success: false, error: 'Operation is required' };
    }

    // Determine output path if not provided
    const finalOutputPath = outputPath ||
                           path.join(
                             path.dirname(imagePath),
                             `${path.basename(imagePath, path.extname(imagePath))}_${operation}${path.extname(imagePath)}`
                           );

    // This would normally use image processing libraries
    // Here we just simulate by copying the image

    fs.copyFileSync(imagePath, finalOutputPath);

    return {
      success: true,
      message: `Image processing simulation for ${operation}`,
      imagePath,
      operation,
      outputPath: finalOutputPath,
      note: "This is a placeholder. In a real implementation, actual image processing would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error processing image: ${error.message}`
    };
  }
}

// Implement executeMachineLearningOperation function
async function executeMachineLearningOperation({ operation, modelType, dataPath, parameters = {} }) {
  try {
    if (!operation) {
      return { success: false, error: 'Operation is required' };
    }

    if (!modelType) {
      return { success: false, error: 'Model type is required' };
    }

    // This would normally use machine learning libraries or APIs
    // Here we just simulate results

    return {
      success: true,
      message: `Machine learning ${operation} simulation for ${modelType}`,
      operation,
      modelType,
      dataPath,
      parameters,
      results: {
        accuracy: 0.85,
        precision: 0.82,
        recall: 0.79,
        f1Score: 0.80
      },
      note: "This is a placeholder. In a real implementation, actual machine learning would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error performing machine learning operation: ${error.message}`
    };
  }
}

// Implement executeCloudResourceManagement function
async function executeCloudResourceManagement({ provider, resourceType, operation, parameters = {} }) {
  try {
    if (!provider) {
      return { success: false, error: 'Cloud provider is required' };
    }

    if (!resourceType) {
      return { success: false, error: 'Resource type is required' };
    }

    if (!operation) {
      return { success: false, error: 'Operation is required' };
    }

    // This would normally use cloud provider SDKs
    // Here we just simulate results

    return {
      success: true,
      message: `Cloud resource ${operation} simulation for ${provider} ${resourceType}`,
      provider,
      resourceType,
      operation,
      parameters,
      resourceId: `${resourceType}-${uuidv4().substring(0, 8)}`,
      note: "This is a placeholder. In a real implementation, actual cloud resources would be managed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error managing cloud resource: ${error.message}`
    };
  }
}

// Implement executeSecurityScan function
async function executeSecurityScan({ targetPath, scanType = 'vulnerability', severity = 'medium' }) {
  try {
    if (!targetPath) {
      return { success: false, error: 'Target path is required' };
    }

    if (!fs.existsSync(targetPath)) {
      return { success: false, error: `Target path does not exist: ${targetPath}` };
    }

    // This would normally use security scanning tools
    // Here we just simulate results

    return {
      success: true,
      message: `Security scan simulation for ${scanType}`,
      targetPath,
      scanType,
      severity,
      findings: [
        { id: 'VULN-001', type: 'dependency', severity: 'high', description: 'Example vulnerability' },
        { id: 'VULN-002', type: 'code', severity: 'medium', description: 'Example security issue' }
      ],
      summary: {
        high: 1,
        medium: 1,
        low: 0,
        total: 2
      },
      note: "This is a placeholder. In a real implementation, actual security scanning would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error performing security scan: ${error.message}`
    };
  }
}

// Implement executeDataTransformation function
async function executeDataTransformation({ inputPath, outputPath, inputFormat, outputFormat, transformations = [] }) {
  try {
    if (!inputPath) {
      return { success: false, error: 'Input path is required' };
    }

    if (!fs.existsSync(inputPath)) {
      return { success: false, error: `Input path does not exist: ${inputPath}` };
    }

    if (!outputPath) {
      return { success: false, error: 'Output path is required' };
    }

    // Determine formats if not provided
    const finalInputFormat = inputFormat || path.extname(inputPath).replace('.', '');
    const finalOutputFormat = outputFormat || path.extname(outputPath).replace('.', '');

    // This would normally use data transformation libraries
    // Here we just copy the file with a note

    // Create output directory if it doesn't exist
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // For transformation simulation, create a simple output file
    fs.writeFileSync(
      outputPath,
      `This is a simulated transformed file.\nOriginal: ${inputPath} (${finalInputFormat})\nTransformed to: ${outputFormat}`,
      'utf8'
    );

    return {
      success: true,
      message: `Data transformation simulation from ${finalInputFormat} to ${finalOutputFormat}`,
      inputPath,
      outputPath,
      inputFormat: finalInputFormat,
      outputFormat: finalOutputFormat,
      transformations,
      note: "This is a placeholder. In a real implementation, actual data transformation would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error transforming data: ${error.message}`
    };
  }
}

// Implement executeMobileAppDevelopment function
async function executeMobileAppDevelopment({ platform, projectPath, operation, parameters = {} }) {
  try {
    if (!platform) {
      return { success: false, error: 'Mobile platform is required' };
    }

    if (!operation) {
      return { success: false, error: 'Operation is required' };
    }

    // Handle different operations
    switch (operation.toLowerCase()) {
      case 'create':
        // Create a new mobile app project
        if (!projectPath) {
          return { success: false, error: 'Project path is required for create operation' };
        }

        if (fs.existsSync(projectPath)) {
          return { success: false, error: `Project directory already exists: ${projectPath}` };
        }

        // Create directory
        fs.mkdirSync(projectPath, { recursive: true });

        // Create basic project structure based on platform
        switch (platform.toLowerCase()) {
          case 'react-native':
            return {
              success: true,
              message: `React Native project created at ${projectPath}`,
              nextSteps: [
                `cd ${projectPath}`,
                `npx react-native init MyApp`
              ],
              note: "This is a placeholder. In a real implementation, a full React Native project would be created."
            };

          case 'flutter':
            return {
              success: true,
              message: `Flutter project created at ${projectPath}`,
              nextSteps: [
                `cd ${projectPath}`,
                `flutter create my_app`
              ],
              note: "This is a placeholder. In a real implementation, a full Flutter project would be created."
            };

          default:
            return { success: false, error: `Unsupported mobile platform: ${platform}` };
        }

      case 'build':
        // Build an existing mobile app project
        if (!projectPath) {
          return { success: false, error: 'Project path is required for build operation' };
        }

        if (!fs.existsSync(projectPath)) {
          return { success: false, error: `Project directory does not exist: ${projectPath}` };
        }

        return {
          success: true,
          message: `${platform} build simulation`,
          platform,
          projectPath,
          note: "This is a placeholder. In a real implementation, the mobile app would be built."
        };

      default:
        return { success: false, error: `Unsupported operation: ${operation}` };
    }
  } catch (error) {
    return {
      success: false,
      error: `Error in mobile app development: ${error.message}`
    };
  }
}

// Implement executeBlockchainOperation function
async function executeBlockchainOperation({ blockchain, operation, parameters = {} }) {
  try {
    if (!blockchain) {
      return { success: false, error: 'Blockchain is required' };
    }

    if (!operation) {
      return { success: false, error: 'Operation is required' };
    }

    // This would normally use blockchain SDKs or APIs
    // Here we just simulate results

    return {
      success: true,
      message: `Blockchain ${operation} simulation for ${blockchain}`,
      blockchain,
      operation,
      parameters,
      transactionId: `0x${uuidv4().replace(/-/g, '')}`,
      note: "This is a placeholder. In a real implementation, actual blockchain operations would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error in blockchain operation: ${error.message}`
    };
  }
}

// Implement executeIoTDeviceManagement function
async function executeIoTDeviceManagement({ deviceId, operation, parameters = {} }) {
  try {
    if (!deviceId) {
      return { success: false, error: 'Device ID is required' };
    }

    if (!operation) {
      return { success: false, error: 'Operation is required' };
    }

    // This would normally use IoT platform SDKs or APIs
    // Here we just simulate results

    return {
      success: true,
      message: `IoT device ${operation} simulation for device ${deviceId}`,
      deviceId,
      operation,
      parameters,
      status: 'success',
      note: "This is a placeholder. In a real implementation, actual IoT device management would be performed."
    };
  } catch (error) {
    return {
      success: false,
      error: `Error in IoT device management: ${error.message}`
    };
  }
}

// The importOpen function is already defined above

// Program initialization
program
  .version('1.0.0')
  .description('Gemini Advanced Agent - An AI-powered command-line assistant')
  .option('-i, --interactive', 'Start in interactive mode')
  .parse(process.argv);

const options = program.opts();

// Process command line options
function processOptions() {
  // Start in interactive mode by default or if explicitly requested
  if (options.interactive || process.argv.length <= 2) {
    interactiveMode();
    return;
  }

  // If we get here, we're not in interactive mode
  console.log(chalk.green('=== Gemini Advanced Agent ==='));
  console.log(chalk.yellow('Running in non-interactive mode'));

  // In the future, we could add support for running specific commands
  // For now, just fall back to interactive mode
  interactiveMode();
}

// Start the agent
processOptions();
