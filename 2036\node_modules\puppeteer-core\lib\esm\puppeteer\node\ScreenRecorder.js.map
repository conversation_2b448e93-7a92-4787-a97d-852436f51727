{"version": 3, "file": "ScreenRecorder.js", "sourceRoot": "", "sources": ["../../../../src/node/ScreenRecorder.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGH,OAAO,EAAC,KAAK,EAAE,SAAS,EAAC,MAAM,oBAAoB,CAAC;AACpD,OAAO,EAAE,MAAM,SAAS,CAAC;AACzB,OAAO,EAAC,WAAW,EAAC,MAAM,aAAa,CAAC;AAExC,OAAO,KAAK,MAAM,OAAO,CAAC;AAG1B,OAAO,EACL,WAAW,EACX,SAAS,EACT,MAAM,EACN,IAAI,EACJ,SAAS,EACT,aAAa,EACb,GAAG,EACH,SAAS,EACT,GAAG,GACJ,MAAM,gCAAgC,CAAC;AACxC,OAAO,EAAC,eAAe,EAAC,MAAM,sBAAsB,CAAC;AAGrD,OAAO,EAAC,UAAU,EAAE,gBAAgB,EAAC,MAAM,mBAAmB,CAAC;AAC/D,OAAO,EAAC,OAAO,EAAC,MAAM,uBAAuB,CAAC;AAC9C,OAAO,EAAC,kBAAkB,EAAC,MAAM,uBAAuB,CAAC;AAEzD,MAAM,SAAS,GAAG,EAAE,CAAC;AACrB,MAAM,WAAW,GAAG,EAAE,CAAC;AAEvB,MAAM,WAAW,GAAG,KAAK,CAAC,kBAAkB,CAAC,CAAC;AAkB9C;;GAEG;IACU,cAAc;sBAAS,WAAW;;;;;iBAAlC,cAAe,SAAQ,WAAW;;;YAsO7C,sDAAA,yBAAA,KAAK,WAAa,MAAc;oBAC9B,MAAM,KAAK,GAAG,MAAM,IAAI,OAAO,CAA2B,OAAO,CAAC,EAAE;wBAClE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;oBAC7C,CAAC,CAAC,CAAC;oBACH,IAAI,KAAK,EAAE,CAAC;wBACV,OAAO,CAAC,GAAG,CAAC,2BAA2B,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;oBAC3D,CAAC;gBACH,CAAC,gBAAA,+HAPK,WAAW,yBAAX,WAAW,6DAOhB;YAQD,+JAAM,IAAI,6DA2BT;;;QA/QD,KAAK,GADM,mDAAc,CACb;QAEZ,QAAQ,CAAiC;QAEzC,WAAW,GAAG,IAAI,eAAe,EAAE,CAAC;QACpC,UAAU,CAAqC;QAE/C,IAAI,CAAS;QAEb;;WAEG;QACH,YACE,IAAU,EACV,KAAa,EACb,MAAc,EACd,EACE,KAAK,EACL,KAAK,EACL,IAAI,EACJ,MAAM,EACN,GAAG,EACH,IAAI,EACJ,KAAK,EACL,OAAO,EACP,MAAM,EACN,IAAI,MACqB,EAAE;YAE7B,KAAK,CAAC,EAAC,aAAa,EAAE,KAAK,EAAC,CAAC,CAAC;YAE9B,MAAM,KAAK,MAAM,CAAC;YAClB,GAAG,KAAK,WAAW,CAAC;YACpB,6CAA6C;YAC7C,IAAI,KAAK,CAAC,CAAC,CAAC;YACZ,KAAK,KAAK,CAAC,CAAC,CAAC;YACb,OAAO,KAAK,SAAS,CAAC;YACtB,MAAM,KAAK,GAAG,CAAC;YACf,IAAI,KAAK,QAAQ,CAAC;YAElB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;YAEhB,4BAA4B;YAC5B,MAAM,EAAC,KAAK,EAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,OAAO,GAAG;gBACd,aAAa,KAAK,YAAY,MAAM,WAAW;gBAC/C,OAAO,KAAK,IAAI,MAAM,MAAM;aAC7B,CAAC;YACF,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC;YAC1C,CAAC;YACD,IAAI,IAAI,EAAE,CAAC;gBACT,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;YACxE,CAAC;YACD,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,IAAI,CAAC,YAAY,KAAK,mBAAmB,CAAC,CAAC;YACrD,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CACpC,MAAM,EACN,GAAG,EACH,IAAI,EACJ,KAAK,EACL,OAAO,EACP,MAAM,CACP,CAAC;YACF,MAAM,EAAE,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC;gBACd,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YACtD,CAAC;YAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CACnB,IAAI;YACJ,6EAA6E;YAC7E;gBACE,CAAC,WAAW,EAAE,OAAO,CAAC;gBACtB,6BAA6B;gBAC7B,CAAC,YAAY,EAAE,QAAQ,CAAC;gBACxB,uEAAuE;gBACvE;oBACE,eAAe;oBACf,GAAG;oBACH,YAAY;oBACZ,IAAI;oBACJ,kBAAkB;oBAClB,GAAG;oBACH,SAAS;oBACT,UAAU;iBACX;gBACD,oEAAoE;gBACpE,gBAAgB;gBAChB,CAAC,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC;gBACnD,iCAAiC;gBACjC,CAAC,IAAI,EAAE,KAAK,CAAC;gBACb,uEAAuE;gBACvE,0CAA0C;gBAC1C,CAAC,UAAU,EAAE,GAAG,CAAC;gBACjB,iDAAiD;gBACjD,CAAC,YAAY,EAAE,GAAG,GAAG,EAAE,CAAC;gBACxB,mBAAmB;gBACnB,CAAC,MAAM,EAAE,GAAG,CAAC;gBACb,kDAAkD;gBAClD,UAAU;gBACV,oDAAoD;gBACpD,6CAA6C;gBAC7C,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC;gBACvB,QAAQ;aACT,CAAC,IAAI,EAAE,EACR,EAAC,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAC,CAClC,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;gBAC/C,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAElB,MAAM,EAAC,MAAM,EAAC,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,GAAG,EAAE;gBAC7C,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACrC,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,GAAG,aAAa,CAC7B,gBAAgB,CAAC,MAAM,EAAE,sBAAsB,CAAC,CAAC,IAAI,CACnD,GAAG,CAAC,KAAK,CAAC,EAAE;gBACV,KAAK,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;oBAC1C,SAAS,EAAE,KAAK,CAAC,SAAS;iBAC3B,CAAC,CAAC;YACL,CAAC,CAAC,EACF,MAAM,CAAC,KAAK,CAAC,EAAE;gBACb,OAAO,KAAK,CAAC,QAAQ,CAAC,SAAS,KAAK,SAAS,CAAC;YAChD,CAAC,CAAC,EACF,GAAG,CAAC,KAAK,CAAC,EAAE;gBACV,OAAO;oBACL,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC;oBACzC,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,SAAU;iBACrC,CAAC;YACJ,CAAC,CAAC,EACF,WAAW,CAAC,CAAC,EAAE,CAAC,CAMf,EACD,SAAS,CAAC,CAAC,CAAC,EAAC,SAAS,EAAE,iBAAiB,EAAE,MAAM,EAAC,EAAE,EAAC,SAAS,EAAC,CAAC,EAAE,EAAE;gBAClE,OAAO,IAAI,CACT,KAAK,CACH,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAC7D,CAAC,IAAI,CAAC,MAAM,CAAC,CACf,CAAC;YACJ,CAAC,CAAC,EACF,GAAG,CAAC,MAAM,CAAC,EAAE;gBACX,KAAK,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;gBAC9B,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,GAAG,EAAE,CAAU,CAAC;YAC9C,CAAC,CAAC,EACF,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CACvD,EACD,EAAC,YAAY,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC,GAAG,EAAE,CAAU,EAAC,CAC9D,CAAC;QACJ,CAAC;QAED,cAAc,CACZ,MAAkB,EAClB,GAA0B,EAC1B,IAAY,EACZ,KAAa,EACb,OAAe,EACf,MAAc;YAEd,MAAM,MAAM,GAAG;gBACb,yBAAyB;gBACzB,CAAC,MAAM,EAAE,KAAK,CAAC;gBACf,sCAAsC;gBACtC,CAAC,MAAM,EAAE,GAAG,OAAO,EAAE,CAAC;gBACtB,8DAA8D;gBAC9D;oBACE,WAAW;oBACX,UAAU;oBACV,WAAW;oBACX,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;iBACvC;aACF,CAAC;YACF,QAAQ,MAAM,EAAE,CAAC;gBACf,KAAK,MAAM;oBACT,OAAO;wBACL,GAAG,MAAM;wBACT,kBAAkB;wBAClB,CAAC,IAAI,EAAE,MAAM,CAAC;qBACf,CAAC,IAAI,EAAE,CAAC;gBACX,KAAK,KAAK;oBACR,GAAG,GAAG,WAAW,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC;oBAC9C,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACtB,IAAI,GAAG,CAAC,CAAC;oBACX,CAAC;oBACD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;wBACjB,WAAW;wBACX,KAAK,IAAI,EAAE,CAAC;oBACd,CAAC;oBACD,OAAO;wBACL,mEAAmE;wBACnE,SAAS;wBACT;4BACE,KAAK;4BACL,OAAO,GAAG,4DAA4D,MAAM,oCAAoC;yBACjH;wBACD,6CAA6C;wBAC7C,CAAC,OAAO,EAAE,GAAG,IAAI,EAAE,CAAC;wBACpB,+CAA+C;wBAC/C,CAAC,cAAc,EAAE,GAAG,KAAK,EAAE,CAAC;wBAC5B,kBAAkB;wBAClB,CAAC,IAAI,EAAE,KAAK,CAAC;qBACd,CAAC,IAAI,EAAE,CAAC;gBACX,KAAK,KAAK;oBACR,OAAO;wBACL,GAAG,MAAM;wBACT,+CAA+C;wBAC/C,CAAC,WAAW,EAAE,mBAAmB,CAAC;wBAClC,kBAAkB;wBAClB,CAAC,IAAI,EAAE,KAAK,CAAC;qBACd,CAAC,IAAI,EAAE,CAAC;YACb,CAAC;QACH,CAAC;QAGD,IAAM,WAAW,mDAOhB;QAED;;;;WAIG;QAEH,KAAK,CAAC,IAAI;YACR,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpC,OAAO;YACT,CAAC;YACD,iDAAiD;YACjD,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAErD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAEzB,kDAAkD;YAClD,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC;YAClD,MAAM,OAAO,CAAC,GAAG,CACf,KAAK,CACH,IAAI,CAAC,GAAG,CACN,CAAC,EACD,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,CACjE,CACF;iBACE,IAAI,CAAC,MAAM,CAAC;iBACZ,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CACpC,CAAC;YAEF,4CAA4C;YAC5C,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;YAC1B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;gBAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC;QACL,CAAC;QAED;;WAEG;QACM,KAAK,CAAC,oCAhDd,OAAO,EAAE,uBAeT,OAAO,EAAE,GAiCM,kBAAkB,EAAC;YACjC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QACpB,CAAC;;;SAvRU,cAAc"}